# 出貨記錄工具 - API介接說明書

## API概述

本文檔描述了出貨記錄工具系統與外部服務的介接方式，包括用戶認證、訂閱驗證、數據同步等功能。

## 1. 認證與授權API

### 1.1 用戶登入驗證

**請求URL：** `/api/auth/login`

**請求方式：** POST

**請求參數：**

| 參數名      | 是否必須 | 類型   | 描述       |
|------------|---------|--------|-----------|
| username   | 是      | String | 用戶名     |
| password   | 是      | String | 密碼       |
| device_info| 是      | Object | 設備信息    |

設備信息包含：

```json
{
  "mac_address": "XX:XX:XX:XX:XX:XX",
  "hostname": "COMPUTER-NAME",
  "ip_address": "***********",
  "os_info": "Windows 10"
}
```

**返回示例：**

```json
{
  "status": 200,
  "message": "登入成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "user_id": 1,
      "username": "admin",
      "role": "admin",
      "real_name": "管理員"
    },
    "permissions": ["user_manage", "shipment_manage", "settings"]
  }
}
```

### 1.2 用戶登出

**請求URL：** `/api/auth/logout`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**返回示例：**

```json
{
  "status": 200,
  "message": "登出成功"
}
```

### 1.3 刷新Token

**請求URL：** `/api/auth/refresh-token`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**返回示例：**

```json
{
  "status": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## 2. 訂閱驗證API

### 2.1 驗證訂閱狀態

**請求URL：** `/api/subscription/validate`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名      | 是否必須 | 類型   | 描述       |
|------------|---------|--------|-----------|
| license_key| 是      | String | 許可證密鑰  |
| device_info| 是      | Object | 設備信息    |

**返回示例：**

```json
{
  "status": 200,
  "message": "訂閱有效",
  "data": {
    "subscription_id": 1,
    "license_key": "XXXX-XXXX-XXXX-XXXX",
    "company_name": "測試公司",
    "start_date": "2023-01-01",
    "end_date": "2024-01-01",
    "max_devices": 5,
    "current_devices": 2,
    "features": ["basic", "advanced_search", "export"],
    "status": 1,
    "days_remaining": 180
  }
}
```

### 2.2 註冊設備

**請求URL：** `/api/subscription/register-device`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名       | 是否必須 | 類型   | 描述       |
|-------------|---------|--------|-----------|
| license_key | 是      | String | 許可證密鑰  |
| device_info | 是      | Object | 設備信息    |
| device_name | 否      | String | 設備名稱    |

**返回示例：**

```json
{
  "status": 200,
  "message": "設備註冊成功",
  "data": {
    "device_id": 3,
    "device_name": "前台電腦",
    "registered_at": "2023-07-15T08:30:00Z",
    "status": 1
  }
}
```

### 2.3 解除設備註冊

**請求URL：** `/api/subscription/unregister-device`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名       | 是否必須 | 類型   | 描述       |
|-------------|---------|--------|-----------|
| device_id   | 是      | Integer| 設備ID     |

**返回示例：**

```json
{
  "status": 200,
  "message": "設備解除註冊成功"
}
```

## 3. 數據同步API

### 3.1 獲取訂單列表

**請求URL：** `/api/orders`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名        | 是否必須 | 類型    | 描述              |
|--------------|---------|---------|------------------|
| page         | 否      | Integer | 頁碼，默認1        |
| limit        | 否      | Integer | 每頁數量，默認20    |
| order_number | 否      | String  | 訂單編號過濾       |
| customer_name| 否      | String  | 客戶名稱過濾       |
| status       | 否      | Integer | 訂單狀態過濾       |
| start_date   | 否      | String  | 開始日期，格式YYYY-MM-DD |
| end_date     | 否      | String  | 結束日期，格式YYYY-MM-DD |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "orders": [
      {
        "order_id": 1,
        "order_number": "ORD20230715001",
        "customer_name": "王小明",
        "order_date": "2023-07-15",
        "total_amount": 1500.00,
        "status": 1,
        "status_text": "待出貨"
      },
      // 更多訂單...
    ]
  }
}
```

### 3.2 獲取訂單詳情

**請求URL：** `/api/orders/{order_id}`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**路徑參數：**

| 參數名    | 是否必須 | 類型    | 描述    |
|----------|---------|---------|--------|
| order_id | 是      | Integer | 訂單ID  |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "order_id": 1,
    "order_number": "ORD20230715001",
    "customer_name": "王小明",
    "customer_phone": "0912345678",
    "customer_address": "台北市信義區信義路五段7號",
    "order_date": "2023-07-15",
    "total_amount": 1500.00,
    "status": 1,
    "status_text": "待出貨",
    "notes": "請於下午送達",
    "items": [
      {
        "item_id": 1,
        "product_id": 101,
        "product_name": "商品A",
        "product_code": "PROD-A",
        "quantity": 2,
        "unit_price": 500.00,
        "shipped_quantity": 0
      },
      {
        "item_id": 2,
        "product_id": 102,
        "product_name": "商品B",
        "product_code": "PROD-B",
        "quantity": 1,
        "unit_price": 500.00,
        "shipped_quantity": 0
      }
    ]
  }
}
```

### 3.3 創建出貨記錄

**請求URL：** `/api/shipments`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

```json
{
  "order_id": 1,
  "tracking_number": "SF1234567890",
  "shipping_method": "宅配",
  "notes": "已確認包裝完好",
  "items": [
    {
      "order_item_id": 1,
      "product_id": 101,
      "serial_number": "SN12345",
      "quantity": 2
    },
    {
      "order_item_id": 2,
      "product_id": 102,
      "serial_number": "",
      "quantity": 1
    }
  ]
}
```

**返回示例：**

```json
{
  "status": 200,
  "message": "出貨記錄創建成功",
  "data": {
    "shipment_id": 1,
    "order_id": 1,
    "tracking_number": "SF1234567890",
    "shipping_method": "宅配",
    "shipped_date": "2023-07-16T10:30:00Z",
    "status": 2,
    "status_text": "已出貨"
  }
}
```

### 3.4 上傳出貨照片

**請求URL：** `/api/shipments/{shipment_id}/photos`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |
| Content-Type  | 是      | multipart/form-data |

**路徑參數：**

| 參數名       | 是否必須 | 類型    | 描述    |
|-------------|---------|---------|--------|
| shipment_id | 是      | Integer | 出貨ID  |

**請求參數：**

| 參數名       | 是否必須 | 類型    | 描述        |
|-------------|---------|---------|------------|
| photo       | 是      | File    | 照片文件     |
| photo_type  | 是      | Integer | 照片類型     |
| description | 否      | String  | 照片描述     |

**返回示例：**

```json
{
  "status": 200,
  "message": "照片上傳成功",
  "data": {
    "photo_id": 1,
    "shipment_id": 1,
    "photo_path": "/uploads/shipments/1/photo_1.jpg",
    "photo_time": "2023-07-16T10:35:00Z",
    "photo_type": 2,
    "photo_type_text": "包裝中",
    "description": "商品包裝照片"
  }
}
```

### 3.5 獲取出貨記錄列表

**請求URL：** `/api/shipments`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名        | 是否必須 | 類型    | 描述              |
|--------------|---------|---------|------------------|
| page         | 否      | Integer | 頁碼，默認1        |
| limit        | 否      | Integer | 每頁數量，默認20    |
| order_number | 否      | String  | 訂單編號過濾       |
| tracking_number| 否     | String  | 追蹤編號過濾       |
| start_date   | 否      | String  | 開始日期，格式YYYY-MM-DD |
| end_date     | 否      | String  | 結束日期，格式YYYY-MM-DD |
| operator_id  | 否      | Integer | 操作員ID過濾       |
| status       | 否      | Integer | 狀態過濾           |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "shipments": [
      {
        "shipment_id": 1,
        "order_id": 1,
        "order_number": "ORD20230715001",
        "tracking_number": "SF1234567890",
        "customer_name": "王小明",
        "shipped_date": "2023-07-16T10:30:00Z",
        "operator_name": "張三",
        "status": 2,
        "status_text": "已出貨",
        "item_count": 3
      },
      // 更多出貨記錄...
    ]
  }
}
```

### 3.6 獲取出貨記錄詳情

**請求URL：** `/api/shipments/{shipment_id}`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**路徑參數：**

| 參數名       | 是否必須 | 類型    | 描述    |
|-------------|---------|---------|--------|
| shipment_id | 是      | Integer | 出貨ID  |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "shipment_id": 1,
    "order_id": 1,
    "order_number": "ORD20230715001",
    "customer_name": "王小明",
    "customer_phone": "0912345678",
    "customer_address": "台北市信義區信義路五段7號",
    "tracking_number": "SF1234567890",
    "shipping_method": "宅配",
    "shipped_date": "2023-07-16T10:30:00Z",
    "operator_id": 2,
    "operator_name": "張三",
    "status": 2,
    "status_text": "已出貨",
    "notes": "已確認包裝完好",
    "items": [
      {
        "shipment_item_id": 1,
        "order_item_id": 1,
        "product_id": 101,
        "product_name": "商品A",
        "product_code": "PROD-A",
        "serial_number": "SN12345",
        "quantity": 2,
        "scan_time": "2023-07-16T10:25:00Z"
      },
      {
        "shipment_item_id": 2,
        "order_item_id": 2,
        "product_id": 102,
        "product_name": "商品B",
        "product_code": "PROD-B",
        "serial_number": "",
        "quantity": 1,
        "scan_time": "2023-07-16T10:28:00Z"
      }
    ],
    "photos": [
      {
        "photo_id": 1,
        "photo_path": "/uploads/shipments/1/photo_1.jpg",
        "photo_time": "2023-07-16T10:35:00Z",
        "photo_type": 2,
        "photo_type_text": "包裝中",
        "description": "商品包裝照片"
      },
      {
        "photo_id": 2,
        "photo_path": "/uploads/shipments/1/photo_2.jpg",
        "photo_time": "2023-07-16T10:40:00Z",
        "photo_type": 3,
        "photo_type_text": "包裝後",
        "description": "最終包裝照片"
      }
    ]
  }
}
```

## 4. 系統設置API

### 4.1 獲取用戶列表

**請求URL：** `/api/users`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名    | 是否必須 | 類型    | 描述          |
|----------|---------|---------|--------------|
| page     | 否      | Integer | 頁碼，默認1    |
| limit    | 否      | Integer | 每頁數量，默認20|
| username | 否      | String  | 用戶名過濾     |
| role     | 否      | String  | 角色過濾       |
| status   | 否      | Integer | 狀態過濾       |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "users": [
      {
        "user_id": 1,
        "username": "admin",
        "real_name": "管理員",
        "email": "<EMAIL>",
        "phone": "0911111111",
        "role": "admin",
        "created_at": "2023-01-01T00:00:00Z",
        "last_login": "2023-07-16T09:00:00Z",
        "status": 1,
        "status_text": "啟用"
      },
      // 更多用戶...
    ]
  }
}
```

### 4.2 創建用戶

**請求URL：** `/api/users`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

```json
{
  "username": "operator1",
  "password": "password123",
  "real_name": "操作員1",
  "email": "<EMAIL>",
  "phone": "0922222222",
  "role": "operator"
}
```

**返回示例：**

```json
{
  "status": 200,
  "message": "用戶創建成功",
  "data": {
    "user_id": 3,
    "username": "operator1",
    "real_name": "操作員1",
    "email": "<EMAIL>",
    "phone": "0922222222",
    "role": "operator",
    "created_at": "2023-07-16T11:00:00Z",
    "status": 1
  }
}
```

### 4.3 獲取設備列表

**請求URL：** `/api/devices`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名    | 是否必須 | 類型    | 描述          |
|----------|---------|---------|--------------|
| page     | 否      | Integer | 頁碼，默認1    |
| limit    | 否      | Integer | 每頁數量，默認20|
| status   | 否      | Integer | 狀態過濾       |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "total": 3,
    "page": 1,
    "limit": 20,
    "devices": [
      {
        "device_id": 1,
        "mac_address": "AA:BB:CC:DD:EE:FF",
        "device_name": "主管電腦",
        "ip_address": "***********00",
        "last_login": "2023-07-16T09:00:00Z",
        "registered_at": "2023-01-01T00:00:00Z",
        "status": 1,
        "status_text": "啟用"
      },
      // 更多設備...
    ]
  }
}
```

### 4.4 獲取系統設置

**請求URL：** `/api/settings`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名       | 是否必須 | 類型   | 描述     |
|-------------|---------|--------|---------|
| setting_group| 否      | String | 設置分組  |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "company": {
      "company_name": "測試公司",
      "company_address": "台北市信義區信義路五段7號",
      "company_phone": "02-12345678",
      "company_email": "<EMAIL>",
      "company_logo": "/uploads/logo.png"
    },
    "app": {
      "default_shipping_method": "宅配",
      "require_photo": "1",
      "auto_logout_time": "30"
    }
  }
}
```

### 4.5 更新系統設置

**請求URL：** `/api/settings`

**請求方式：** PUT

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

```json
{
  "settings": [
    {
      "setting_key": "company_name",
      "setting_value": "新測試公司",
      "setting_group": "company"
    },
    {
      "setting_key": "require_photo",
      "setting_value": "0",
      "setting_group": "app"
    }
  ]
}
```

**返回示例：**

```json
{
  "status": 200,
  "message": "設置更新成功"
}
```

## 5. 離線操作與數據同步

### 5.1 檢查數據更新

**請求URL：** `/api/sync/check`

**請求方式：** GET

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

| 參數名      | 是否必須 | 類型    | 描述            |
|------------|---------|---------|----------------|
| last_sync  | 否      | String  | 上次同步時間     |

**返回示例：**

```json
{
  "status": 200,
  "message": "success",
  "data": {
    "has_updates": true,
    "updates": {
      "orders": 5,
      "products": 2,
      "settings": 1
    },
    "current_server_time": "2023-07-16T12:00:00Z"
  }
}
```

### 5.2 同步離線數據

**請求URL：** `/api/sync/upload`

**請求方式：** POST

**請求頭：**

| 參數名         | 是否必須 | 描述       |
|---------------|---------|-----------|
| Authorization | 是      | Bearer token |

**請求參數：**

```json
{
  "shipments": [
    {
      "local_id": "local-1",
      "order_id": 1,
      "tracking_number": "SF1234567890",
      "shipping_method": "宅配",
      "shipped_date": "2023-07-16T10:30:00Z",
      "operator_id": 2,
      "status": 2,
      "notes": "離線建立的出貨",
      "items": [
        {
          "order_item_id": 1,
          "product_id": 101,
          "serial_number": "SN12345",
          "quantity": 2,
          "scan_time": "2023-07-16T10:25:00Z"
        }
      ]
    }
  ],
  "photos": [
    {
      "local_shipment_id": "local-1",
      "photo_data": "base64編碼的照片數據",
      "photo_time": "2023-07-16T10:35:00Z",
      "photo_type": 2,
      "description": "離線拍攝的照片"
    }
  ]
}
```

**返回示例：**

```json
{
  "status": 200,
  "message": "離線數據同步成功",
  "data": {
    "shipments": [
      {
        "local_id": "local-1",
        "server_id": 5,
        "status": "success"
      }
    ],
    "photos": [
      {
        "local_shipment_id": "local-1",
        "server_photo_id": 10,
        "status": "success"
      }
    ]
  }
}
``` 