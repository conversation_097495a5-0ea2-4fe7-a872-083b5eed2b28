# 出貨記錄工具使用說明

## 專案概述

出貨記錄工具是一個基於PySide6開發的桌面應用程序，用於管理和記錄企業的出貨過程。通過掃碼記錄每個包裹的內容物，拍照保存包裝情況，避免出貨爭議，提高出貨效率和準確性。

## 主要功能

- **用戶認證與訂閱管理**：支持多用戶登入，並驗證訂閱狀態
- **出貨查詢**：根據多種條件查詢出貨記錄
- **進行出貨**：掃描條碼確認商品，拍照記錄包裝情況
- **出貨記錄**：查看歷史出貨記錄，導出報表
- **系統設置**：管理用戶、設備、系統參數等
- **離線操作**：支持無網絡環境下的出貨操作，連網後自動同步

## 系統要求

- **作業系統**：Windows 10/11、macOS 10.15+、Linux (Ubuntu 20.04+)
- **最低硬件配置**：
  - 處理器：雙核心 2.0GHz 以上
  - 內存：4GB RAM 以上
  - 硬盤：200MB 可用空間
  - 顯示器：1366x768 分辨率以上
- **可選設備**：
  - 條碼掃描器
  - 攝像頭
  - 印表機

## 安裝指南

### Windows

1. 下載最新的安裝包 `ShipmentRecorder-Setup-x.x.x.exe`
2. 雙擊安裝包運行安裝程序
3. 按照安裝嚮導完成安裝
4. 在開始菜單或桌面快捷方式啟動應用

### macOS

1. 下載最新的 `ShipmentRecorder-x.x.x.dmg` 文件
2. 雙擊打開DMG文件
3. 將應用拖拽到應用程序文件夾
4. 從啟動器或應用程序文件夾啟動應用

### Linux

```bash
# 使用AppImage
chmod +x ShipmentRecorder-x.x.x.AppImage
./ShipmentRecorder-x.x.x.AppImage
```

## 快速入門

### 首次使用

1. 啟動應用後，您將看到登入界面
2. 輸入管理員帳號和密碼（默認：admin/admin）
3. 首次登入會提示更改密碼
4. 首次登入還需要輸入許可證密鑰進行訂閱驗證
5. 驗證成功後，您將進入主界面

### 基本操作流程

#### 進行出貨

1. 在主界面左側導航欄選擇「進行出貨」
2. 掃描或輸入訂單編號
3. 系統顯示訂單信息和需要出貨的商品列表
4. 逐個掃描商品條碼進行確認
5. 點擊「拍照記錄」按鈕，拍攝包裝照片
6. 填寫運送方式等信息
7. 點擊「完成出貨」按鈕

#### 查詢出貨

1. 在主界面左側導航欄選擇「查詢出貨」
2. 輸入訂單編號、客戶名稱或選擇日期範圍
3. 點擊「查詢」按鈕
4. 在結果列表中點擊「詳情」查看具體出貨信息

## 配置指南

### 掃碼槍設置

1. 連接掃碼槍到電腦
2. 在「系統設置」->「設備設置」中配置掃碼槍參數
3. 測試掃描功能是否正常

### 攝像頭設置

1. 確保攝像頭已連接並被系統識別
2. 在「系統設置」->「設備設置」中選擇攝像頭
3. 調整分辨率和圖片質量

### 印表機設置

1. 安裝並配置系統印表機
2. 在「系統設置」->「設備設置」中選擇默認印表機
3. 設置標籤格式和打印參數

## 用戶管理

### 添加新用戶

1. 以管理員身份登入
2. 進入「系統設置」->「用戶管理」
3. 點擊「添加用戶」按鈕
4. 填寫用戶信息，設置權限
5. 保存

### 重置密碼

1. 以管理員身份登入
2. 進入「系統設置」->「用戶管理」
3. 選擇要重置密碼的用戶
4. 點擊「重置密碼」按鈕
5. 輸入新密碼並確認

## 數據管理

### 數據備份

1. 進入「系統設置」->「系統參數」
2. 點擊「備份數據」按鈕
3. 選擇備份存儲位置
4. 等待備份完成

### 數據恢復

1. 進入「系統設置」->「系統參數」
2. 點擊「恢復數據」按鈕
3. 選擇備份文件
4. 確認恢復操作

## 常見問題

### 掃碼槍無法識別條碼

- 檢查掃碼槍連接是否正常
- 確認條碼清晰可見，沒有損壞
- 調整掃碼槍的距離和角度
- 檢查掃碼槍的配置參數

### 同步失敗

- 檢查網絡連接
- 確認伺服器地址設置正確
- 檢查用戶認證是否有效
- 嘗試手動觸發同步

### 應用崩潰

- 檢查系統日誌了解崩潰原因
- 確保使用最新版本的應用
- 驗證數據庫文件是否損壞
- 聯繫技術支持提供錯誤報告

## 聯繫與支持

- **技術支持**：<EMAIL>
- **問題報告**：<EMAIL>
- **官方網站**：https://www.example.com
- **電話支持**：+886-2-12345678 (週一至週五 9:00-18:00)

## 開發文檔

如需瞭解更多開發相關信息，請參考以下文檔：

- [項目需求書](項目需求書.md)
- [開發規則書](開發規則書.md)
- [數據庫結構](DB數據結構表.md)
- [應用結構](網站樹狀結構.md)
- [API介接說明書](後台API介接說明書.md)
- [開發進度報告](開發進度報告.md)

## 版權聲明

© 2023 Example Company. 保留所有權利。

未經授權，不得進行商業使用或二次開發。 