# 出貨記錄工具 - 開發規則書

## 1. 開發環境

### 1.1 開發工具與環境
- **作業系統**：支持 Windows 10/11、macOS 和 Linux
- **IDE**：PyCharm 或 VS Code（推薦）
- **Python 版本**：Python 3.9 或以上
- **版本控制**：Git
- **框架**：PySide6（Qt 6.4+）
- **數據庫**：SQLite 3（本地開發與部署）

### 1.2 必要依賴
- **PySide6**：用於GUI開發
- **SQLAlchemy**：用於ORM操作數據庫
- **Pillow**：用於圖像處理
- **requests**：用於網絡請求
- **uuid**：用於生成唯一標識符
- **bcrypt**：用於密碼哈希
- **PyJWT**：用於JWT token生成與驗證
- **getmac**：用於獲取MAC地址

## 2. 專案結構

```
出貨記錄工具/
│
├── src/                      # 源代碼目錄
│   ├── main.py               # 應用入口點
│   ├── app.py                # 主應用邏輯
│   │
│   ├── auth/                 # 認證相關模塊
│   │   ├── __init__.py
│   │   ├── login.py          # 登入邏輯
│   │   ├── device.py         # 設備識別
│   │   └── subscription.py   # 訂閱驗證
│   │
│   ├── ui/                   # UI相關模塊
│   │   ├── __init__.py
│   │   ├── main_window.py    # 主窗口
│   │   ├── login_window.py   # 登入窗口
│   │   ├── query_shipment.py # 查詢出貨頁面
│   │   ├── process_shipment.py # 進行出貨頁面
│   │   ├── shipment_history.py # 出貨記錄頁面
│   │   └── settings.py       # 系統設置頁面
│   │
│   ├── models/               # 數據模型
│   │   ├── __init__.py
│   │   ├── base.py           # 基礎模型
│   │   ├── user.py           # 用戶模型
│   │   ├── subscription.py   # 訂閱模型
│   │   ├── order.py          # 訂單模型
│   │   ├── product.py        # 產品模型
│   │   ├── shipment.py       # 出貨模型
│   │   └── settings.py       # 設置模型
│   │
│   ├── database/             # 數據庫操作
│   │   ├── __init__.py
│   │   ├── db.py             # 數據庫連接與初始化
│   │   └── migrations/       # 數據庫遷移腳本
│   │
│   ├── services/             # 業務邏輯服務
│   │   ├── __init__.py
│   │   ├── auth_service.py   # 認證服務
│   │   ├── order_service.py  # 訂單服務
│   │   ├── shipment_service.py # 出貨服務
│   │   ├── product_service.py # 產品服務
│   │   └── settings_service.py # 設置服務
│   │
│   ├── utils/                # 通用工具
│   │   ├── __init__.py
│   │   ├── logger.py         # 日誌工具
│   │   ├── encryption.py     # 加密工具
│   │   ├── network.py        # 網絡工具
│   │   ├── hardware.py       # 硬件信息工具
│   │   └── validation.py     # 驗證工具
│   │
│   └── api/                 # API客戶端
│       ├── __init__.py
│       ├── client.py        # API客戶端基類
│       ├── auth_api.py      # 認證API
│       └── sync_api.py      # 同步API
│
├── resources/               # 資源文件
│   ├── icons/               # 圖標
│   ├── styles/              # 樣式表
│   └── locales/             # 語言包
│
├── database/                # 數據庫文件
│   └── database.sqlite      # SQLite數據庫文件
│
├── tests/                   # 測試
│   ├── unit/                # 單元測試
│   └── integration/         # 集成測試
│
├── docs/                    # 文檔
│   ├── 手冊/                 # 各種手冊和文檔
│   └── api/                 # API文檔
│
├── requirements.txt         # 依賴列表
├── setup.py                 # 打包腳本
└── README.md                # 項目說明
```

## 3. 編碼規範

### 3.1 Python編碼規範
- 遵循**PEP 8**編碼風格標準
- 使用4個空格進行縮進，不使用Tab
- 函數、變量名採用小駝峰命名法（例如：`getUserInfo()`）
- 類名採用大駝峰命名法（例如：`UserService`）
- 常量名全大寫，用下劃線分隔（例如：`MAX_RETRY_COUNT`）
- 每行代碼不超過79個字符
- 註釋與代碼保持一致，及時更新
- 使用類型提示（Type Hints）增強代碼可讀性

### 3.2 數據庫規範
- 表名使用小寫，單數形式（例如：`user`而非`users`）
- 主鍵統一命名為`id`
- 外鍵命名規則：`{關聯表名}_id`
- 創建與更新時間字段命名：`created_at`、`updated_at`
- 所有表必須包含`status`字段，用於軟刪除
- 查詢時優先使用參數化查詢，避免SQL注入
- 大型查詢考慮分頁處理

### 3.3 UI設計規範
- 所有界面統一使用Qt樣式表進行樣式設計
- 必須支持高DPI屏幕
- 色彩搭配：主色調#3498db、輔助色調#2ecc71、警告色#e74c3c
- 字體：默認使用系統字體，大小12-14pt
- 圖標：優先使用SVG矢量圖標，保證在不同分辨率下顯示清晰
- 所有界面元素必須有適當的提示文本（tooltip）
- 主要操作按鈕放置在右下角，按從左到右的順序：取消、上一步、下一步、確認
- 消息提示統一使用對話框，包含適當的圖標

## 4. 業務邏輯規範

### 4.1 認證與授權
- 密碼必須加鹽處理後再進行哈希存儲
- 用戶登入後生成JWT Token，有效期為24小時
- 權限檢查在每個需要權限的操作前進行
- 設備驗證基於MAC地址與用戶綁定

### 4.2 訂閱管理
- 訂閱狀態每次啟動應用時進行驗證
- 訂閱過期前7天開始顯示提醒
- 訂閱過期後僅允許查詢功能，禁止進行出貨操作

### 4.3 出貨流程
1. 選擇/掃描訂單號
2. 系統顯示訂單詳情與需要出貨的商品
3. 操作人員掃描商品條碼進行確認
4. 所有商品掃描完成後，拍照記錄包裝情況
5. 確認出貨信息，生成出貨單

### 4.4 數據同步
- 本地操作優先存儲在SQLite數據庫
- 有網絡連接時自動與服務器同步
- 無網絡時可離線操作，連接後自動同步
- 同步衝突時以服務器數據為準，但保留本地歷史記錄

## 5. 錯誤處理與日誌規範

### 5.1 異常處理
- 使用try-except捕獲所有可能的異常
- 對用戶顯示友好的錯誤信息，技術細節記錄到日誌
- 關鍵操作（如出貨確認）需要二次確認
- 數據庫操作錯誤需要回滾事務

### 5.2 日誌規範
- 日誌分級：DEBUG, INFO, WARNING, ERROR, CRITICAL
- 日誌內容包含：時間戳、級別、來源模塊、消息內容
- 敏感信息（如密碼）不得寫入日誌
- 日誌文件按日期滾動存儲，保留最近30天
- 錯誤日誌同時記錄到文件與控制台

## 6. 測試規範

### 6.1 單元測試
- 所有業務邏輯函數必須有對應的單元測試
- 測試覆蓋率目標：80%以上
- 使用pytest框架進行測試
- 測試數據使用固定的測試集，不依賴真實數據

### 6.2 集成測試
- 主要功能流程必須有端到端的集成測試
- 測試環境與開發環境隔離
- 每次發布前必須通過所有集成測試

## 7. 發布與部署規範

### 7.1 版本管理
- 遵循語義化版本規則：主版本號.次版本號.修訂號
- 主版本號：不兼容的API修改
- 次版本號：向下兼容的功能性新增
- 修訂號：向下兼容的問題修正

### 7.2 打包與部署
- Windows版本使用Inno Setup創建安裝程序
- macOS版本使用DMG格式打包
- Linux版本提供AppImage格式
- 配置文件與數據分離，存放在用戶數據目錄
- 支持自動更新功能

## 8. 安全規範

### 8.1 數據安全
- 本地數據庫文件必須加密存儲
- 敏感信息（如密碼）必須使用bcrypt或同等強度算法加密
- 定期自動備份數據，並提供恢復功能
- 用戶退出時清除敏感緩存數據

### 8.2 傳輸安全
- 所有API請求必須使用HTTPS
- 敏感信息傳輸前必須加密
- API訪問需要認證Token
- 限制API請求頻率，防止暴力攻擊

## 9. 文檔規範

### 9.1 代碼文檔
- 所有公共函數、類、模塊必須有文檔字符串(docstring)
- 使用Google風格的docstring
- 複雜邏輯必須有詳細註釋

### 9.2 用戶文檔
- 提供詳細的用戶手冊，包括所有功能的使用說明
- 常見問題與解答(FAQ)
- 視頻教學材料

## 10. 性能優化規則

### 10.1 響應時間
- 用戶交互響應時間不超過0.1秒
- 數據加載操作不超過2秒
- 長時間操作必須顯示進度條

### 10.2 資源使用
- 內存占用不超過300MB
- CPU使用率平均不超過20%
- 數據庫查詢必須優化，大表需要創建適當索引
- 圖片等資源必須壓縮處理，減少磁盤與內存占用 