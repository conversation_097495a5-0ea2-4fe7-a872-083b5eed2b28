# 出貨記錄工具 - 數據庫結構設計

## 數據庫概述
本系統使用SQLite作為數據庫引擎，主要存儲用戶信息、訂閱狀態、出貨記錄等數據。

## 數據表結構

### 1. 用戶表 (users)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| user_id | INTEGER | 是 | 否 | 用戶ID，自增 |
| username | TEXT | 否 | 否 | 用戶名稱 |
| password_hash | TEXT | 否 | 否 | 密碼哈希 |
| salt | TEXT | 否 | 否 | 密碼鹽值 |
| real_name | TEXT | 否 | 是 | 用戶真實姓名 |
| email | TEXT | 否 | 是 | 電子郵件 |
| phone | TEXT | 否 | 是 | 電話號碼 |
| role | TEXT | 否 | 否 | 角色(admin/operator) |
| created_at | TIMESTAMP | 否 | 否 | 創建時間 |
| updated_at | TIMESTAMP | 否 | 否 | 更新時間 |
| last_login | TIMESTAMP | 否 | 是 | 最後登入時間 |
| status | INTEGER | 否 | 否 | 狀態(1=啟用, 0=禁用) |

### 2. 訂閱表 (subscriptions)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| subscription_id | INTEGER | 是 | 否 | 訂閱ID，自增 |
| license_key | TEXT | 否 | 否 | 許可證密鑰 |
| company_name | TEXT | 否 | 是 | 公司名稱 |
| start_date | DATE | 否 | 否 | 開始日期 |
| end_date | DATE | 否 | 否 | 結束日期 |
| max_devices | INTEGER | 否 | 否 | 最大設備數 |
| features | TEXT | 否 | 是 | 功能列表(JSON) |
| status | INTEGER | 否 | 否 | 狀態(1=啟用, 0=禁用) |

### 3. 設備表 (devices)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| device_id | INTEGER | 是 | 否 | 設備ID，自增 |
| subscription_id | INTEGER | 否 | 否 | 關聯的訂閱ID |
| mac_address | TEXT | 否 | 否 | MAC地址 |
| device_name | TEXT | 否 | 是 | 設備名稱 |
| last_login | TIMESTAMP | 否 | 是 | 最後登入時間 |
| ip_address | TEXT | 否 | 是 | IP地址 |
| registered_at | TIMESTAMP | 否 | 否 | 註冊時間 |
| status | INTEGER | 否 | 否 | 狀態(1=啟用, 0=禁用) |

### 4. 登入記錄表 (login_logs)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| log_id | INTEGER | 是 | 否 | 日誌ID，自增 |
| user_id | INTEGER | 否 | 否 | 用戶ID |
| device_id | INTEGER | 否 | 否 | 設備ID |
| login_time | TIMESTAMP | 否 | 否 | 登入時間 |
| logout_time | TIMESTAMP | 否 | 是 | 登出時間 |
| ip_address | TEXT | 否 | 是 | IP地址 |
| status | INTEGER | 否 | 否 | 狀態(1=成功, 0=失敗) |
| failure_reason | TEXT | 否 | 是 | 失敗原因 |

### 5. 訂單表 (orders)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| order_id | INTEGER | 是 | 否 | 訂單ID，自增 |
| order_number | TEXT | 否 | 否 | 訂單編號 |
| customer_name | TEXT | 否 | 是 | 客戶名稱 |
| customer_phone | TEXT | 否 | 是 | 客戶電話 |
| customer_address | TEXT | 否 | 是 | 客戶地址 |
| order_date | DATE | 否 | 否 | 訂單日期 |
| total_amount | REAL | 否 | 否 | 總金額 |
| status | INTEGER | 否 | 否 | 狀態(1=待出貨, 2=已出貨, 3=已取消) |
| notes | TEXT | 否 | 是 | 備註 |

### 6. 訂單商品表 (order_items)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| item_id | INTEGER | 是 | 否 | 商品ID，自增 |
| order_id | INTEGER | 否 | 否 | 訂單ID |
| product_id | INTEGER | 否 | 否 | 產品ID |
| product_name | TEXT | 否 | 否 | 產品名稱 |
| product_code | TEXT | 否 | 是 | 產品編碼 |
| quantity | INTEGER | 否 | 否 | 數量 |
| unit_price | REAL | 否 | 否 | 單價 |
| shipped_quantity | INTEGER | 否 | 否 | 已出貨數量 |

### 7. 產品表 (products)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| product_id | INTEGER | 是 | 否 | 產品ID，自增 |
| product_name | TEXT | 否 | 否 | 產品名稱 |
| product_code | TEXT | 否 | 否 | 產品編碼 |
| barcode | TEXT | 否 | 是 | 條碼 |
| category | TEXT | 否 | 是 | 分類 |
| description | TEXT | 否 | 是 | 描述 |
| unit | TEXT | 否 | 是 | 單位 |
| unit_price | REAL | 否 | 是 | 單價 |
| image_path | TEXT | 否 | 是 | 圖片路徑 |
| status | INTEGER | 否 | 否 | 狀態(1=啟用, 0=禁用) |

### 8. 出貨記錄表 (shipments)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| shipment_id | INTEGER | 是 | 否 | 出貨ID，自增 |
| order_id | INTEGER | 否 | 否 | 訂單ID |
| tracking_number | TEXT | 否 | 是 | 追蹤編號 |
| shipping_method | TEXT | 否 | 是 | 運送方式 |
| shipped_date | TIMESTAMP | 否 | 否 | 出貨日期 |
| operator_id | INTEGER | 否 | 否 | 操作員ID |
| status | INTEGER | 否 | 否 | 狀態(1=包裝中, 2=已出貨, 3=已送達) |
| notes | TEXT | 否 | 是 | 備註 |

### 9. 出貨商品表 (shipment_items)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| shipment_item_id | INTEGER | 是 | 否 | 出貨商品ID，自增 |
| shipment_id | INTEGER | 否 | 否 | 出貨ID |
| order_item_id | INTEGER | 否 | 否 | 訂單商品ID |
| product_id | INTEGER | 否 | 否 | 產品ID |
| serial_number | TEXT | 否 | 是 | 序號 |
| quantity | INTEGER | 否 | 否 | 數量 |
| scan_time | TIMESTAMP | 否 | 否 | 掃描時間 |
| notes | TEXT | 否 | 是 | 備註 |

### 10. 出貨照片表 (shipment_photos)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| photo_id | INTEGER | 是 | 否 | 照片ID，自增 |
| shipment_id | INTEGER | 否 | 否 | 出貨ID |
| photo_path | TEXT | 否 | 否 | 照片路徑 |
| photo_time | TIMESTAMP | 否 | 否 | 拍照時間 |
| photo_type | INTEGER | 否 | 否 | 照片類型(1=包裝前, 2=包裝中, 3=包裝後) |
| description | TEXT | 否 | 是 | 描述 |

### 11. 系統設置表 (settings)
| 欄位名稱 | 數據類型 | 主鍵 | 可空 | 描述 |
|----------|----------|------|------|------|
| setting_id | INTEGER | 是 | 否 | 設置ID，自增 |
| setting_key | TEXT | 否 | 否 | 設置鍵 |
| setting_value | TEXT | 否 | 是 | 設置值 |
| setting_group | TEXT | 否 | 是 | 設置分組 |
| description | TEXT | 否 | 是 | 描述 |
| updated_at | TIMESTAMP | 否 | 否 | 更新時間 |

## 數據關係

1. users (1) -> (多) login_logs
2. subscriptions (1) -> (多) devices
3. devices (1) -> (多) login_logs
4. users (1) -> (多) shipments
5. orders (1) -> (多) order_items
6. orders (1) -> (多) shipments
7. products (1) -> (多) order_items
8. shipments (1) -> (多) shipment_items
9. shipments (1) -> (多) shipment_photos
10. order_items (1) -> (多) shipment_items 