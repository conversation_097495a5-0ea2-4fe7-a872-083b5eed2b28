#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 主程序入口

此模塊是應用程序的入口點，負責初始化應用程序環境並啟動圖形用戶界面。
包括檢查環境、初始化資源、配置日誌、連接數據庫和啟動UI。
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加項目根目錄到Python路徑
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, ROOT_DIR)

from src.utils.logger import setup_logger
from src.app import Application
from src.utils.hardware import get_mac_address
from src.database.db import initialize_database


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='出貨記錄工具')
    parser.add_argument('-d', '--debug', action='store_true', help='啟用調試模式')
    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='設置日誌級別')
    parser.add_argument('--data-dir', type=str, help='指定數據目錄')
    return parser.parse_args()


def setup_environment(args):
    """設置應用環境"""
    # 配置數據目錄
    if args.data_dir:
        data_dir = Path(args.data_dir)
    else:
        # 默認數據目錄
        if sys.platform == 'win32':
            app_data = os.environ.get('APPDATA', '')
            data_dir = Path(app_data) / 'ShipmentRecorder'
        elif sys.platform == 'darwin':
            data_dir = Path.home() / 'Library' / 'Application Support' / 'ShipmentRecorder'
        else:  # Linux and others
            data_dir = Path.home() / '.shipment_recorder'

    # 創建必要的目錄
    data_dir.mkdir(parents=True, exist_ok=True)
    (data_dir / 'logs').mkdir(exist_ok=True)
    (data_dir / 'database').mkdir(exist_ok=True)
    (data_dir / 'photos').mkdir(exist_ok=True)
    (data_dir / 'backups').mkdir(exist_ok=True)
    (data_dir / 'temp').mkdir(exist_ok=True)

    # 設置環境變量
    os.environ['SHIPMENT_RECORDER_DATA_DIR'] = str(data_dir)
    os.environ['SHIPMENT_RECORDER_DEBUG'] = '1' if args.debug else '0'

    # 配置日誌
    log_level = getattr(logging, args.log_level)
    log_file = data_dir / 'logs' / 'app.log'
    setup_logger(log_file, log_level)

    # 返回設置的環境信息
    return {
        'data_dir': data_dir,
        'debug_mode': args.debug,
        'log_level': args.log_level,
        'mac_address': get_mac_address()
    }


def main():
    """應用程序主入口"""
    args = parse_arguments()
    env_info = setup_environment(args)
    
    logging.info("啟動出貨記錄工具...")
    logging.debug(f"環境信息: {env_info}")
    
    # 初始化數據庫
    db_path = env_info['data_dir'] / 'database' / 'database.sqlite'
    db_url = f"sqlite:///{db_path}"
    initialize_database(db_url)
    
    # 啟動應用
    app = Application(env_info)
    exit_code = app.run()
    
    logging.info(f"應用程序退出，退出碼: {exit_code}")
    return exit_code


if __name__ == "__main__":
    sys.exit(main()) 