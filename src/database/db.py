#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 數據庫初始化

此模塊負責初始化和連接數據庫。
"""

import os
import sys
import logging
from typing import Optional

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session

# 定義數據庫基類
Base = declarative_base()

# 全局變量
engine = None
SessionLocal = None
db_session = None


def initialize_database(db_url: Optional[str] = None) -> None:
    """
    初始化數據庫連接
    
    Args:
        db_url: 數據庫URL，如果為None則使用默認路徑
    """
    global engine, SessionLocal, db_session
    
    if db_url is None:
        # 獲取項目根目錄
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        # 使用默認數據庫路徑
        db_path = os.path.join(root_dir, 'database', 'database.sqlite')
        # 確保數據庫目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        db_url = f"sqlite:///{db_path}"
    
    logging.info(f"初始化數據庫連接: {db_url}")
    
    # 創建SQLAlchemy引擎
    engine = create_engine(
        db_url,
        connect_args={"check_same_thread": False} if db_url.startswith('sqlite') else {},
        echo=False,
    )
    
    # 創建會話工廠
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db_session = scoped_session(SessionLocal)
    
    # 創建所有表（如果不存在）
    from . import models
    try:
        Base.metadata.create_all(bind=engine)
        logging.info("數據庫表已創建/驗證")
    except Exception as e:
        logging.error(f"數據庫初始化失敗: {str(e)}")
        raise


def get_db_session():
    """
    獲取數據庫會話
    
    Yields:
        sqlalchemy.orm.Session: 數據庫會話
    """
    if db_session is None:
        raise RuntimeError("數據庫尚未初始化，請先調用initialize_database")
    
    session = db_session()
    try:
        yield session
    finally:
        session.close()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 