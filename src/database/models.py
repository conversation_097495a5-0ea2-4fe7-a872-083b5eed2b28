#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 數據庫模型

此模塊定義了數據庫表模型。
"""

import datetime
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .db import Base


class User(Base):
    """用戶表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password = Column(String(128), nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    role = Column(String(20), nullable=False, default='user')
    created_at = Column(DateTime, nullable=False, default=func.now())
    last_login = Column(DateTime, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    
    # 關聯
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")
    devices = relationship("Device", back_populates="user", cascade="all, delete-orphan")
    password_resets = relationship("PasswordReset", back_populates="user", cascade="all, delete-orphan")
    shipments = relationship("Shipment", back_populates="operator")
    customers = relationship("Customer", back_populates="created_by")
    products = relationship("Product", back_populates="created_by")
    
    def __repr__(self):
        return f"<User {self.username}>"


class Subscription(Base):
    """訂閱表"""
    __tablename__ = "subscription"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    start_date = Column(Date, nullable=False)
    expiry_date = Column(Date, nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)
    
    # 關聯
    user = relationship("User", back_populates="subscriptions")
    
    def __repr__(self):
        return f"<Subscription {self.id} for user_id={self.user_id}>"


class Device(Base):
    """設備表"""
    __tablename__ = "devices"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    mac_address = Column(String(50), nullable=False)
    device_name = Column(String(100), nullable=False)
    last_login = Column(DateTime, nullable=False)
    
    # 關聯
    user = relationship("User", back_populates="devices")
    
    __table_args__ = (
        # 每個用戶的MAC地址應該是唯一的
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4'}
    )
    
    def __repr__(self):
        return f"<Device {self.device_name} ({self.mac_address})>"


class PasswordReset(Base):
    """密碼重置表"""
    __tablename__ = "password_reset"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    reset_token = Column(String(128), unique=True, nullable=False)
    expiry_date = Column(DateTime, nullable=False)
    is_used = Column(Boolean, nullable=False, default=False)
    
    # 關聯
    user = relationship("User", back_populates="password_resets")
    
    def __repr__(self):
        return f"<PasswordReset for user_id={self.user_id}>"


class Customer(Base):
    """客戶表"""
    __tablename__ = "customers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    contact_person = Column(String(100), nullable=True)
    phone = Column(String(50), nullable=True)
    email = Column(String(100), nullable=True)
    address = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True, onupdate=func.now())
    
    # 關聯
    created_by = relationship("User", back_populates="customers")
    shipments = relationship("Shipment", back_populates="customer")
    
    def __repr__(self):
        return f"<Customer {self.name}>"


class Product(Base):
    """產品表"""
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    sku = Column(String(50), unique=True, nullable=False)
    barcode = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    unit = Column(String(20), nullable=True)
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True, onupdate=func.now())
    
    # 關聯
    created_by = relationship("User", back_populates="products")
    shipment_items = relationship("ShipmentItem", back_populates="product")
    
    def __repr__(self):
        return f"<Product {self.name} ({self.sku})>"


class Shipment(Base):
    """出貨表"""
    __tablename__ = "shipments"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    shipment_number = Column(String(50), unique=True, nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    shipment_date = Column(Date, nullable=False)
    status = Column(String(20), nullable=False, default='pending')
    notes = Column(Text, nullable=True)
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=True, onupdate=func.now())
    
    # 關聯
    customer = relationship("Customer", back_populates="shipments")
    operator = relationship("User", back_populates="shipments")
    items = relationship("ShipmentItem", back_populates="shipment", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Shipment {self.shipment_number}>"


class ShipmentItem(Base):
    """出貨項目表"""
    __tablename__ = "shipment_items"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    shipment_id = Column(Integer, ForeignKey("shipments.id", ondelete="CASCADE"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(Float, nullable=False)
    serial_number = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    
    # 關聯
    shipment = relationship("Shipment", back_populates="items")
    product = relationship("Product", back_populates="shipment_items")
    
    def __repr__(self):
        return f"<ShipmentItem {self.id} for shipment_id={self.shipment_id}>" 