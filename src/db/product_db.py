#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 產品數據庫操作模塊

此模塊提供與產品相關的數據庫操作。
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import time

from .database import get_db_instance


class ProductDB:
    """產品數據庫操作類"""
    
    def __init__(self):
        """初始化"""
        self.db = get_db_instance()
    
    def add_product(self, user_id: int, product_data: Dict[str, Any]) -> Optional[int]:
        """
        添加產品
        
        Args:
            user_id: 用户ID
            product_data: 產品數據
        
        Returns:
            新添加的產品ID，如果添加失敗則返回None
        """
        try:
            # 確保必要字段存在
            required_fields = ['product_name', 'product_code']
            for field in required_fields:
                if field not in product_data:
                    logging.error(f"添加產品失敗: 缺少必要字段 {field}")
                    return None
            
            # 檢查產品代碼是否已存在
            existing = self.db.fetch_one(
                "SELECT * FROM products WHERE product_code = ? AND user_id = ?",
                (product_data['product_code'], user_id)
            )
            
            if existing:
                logging.error(f"添加產品失敗: 產品代碼已存在 (product_code: {product_data['product_code']})")
                return None
            
            # 添加用户ID和創建時間
            product_data['user_id'] = user_id
            if 'created_at' not in product_data:
                product_data['created_at'] = int(time.time())
            
            # 插入產品
            product_id = self.db.insert('products', product_data)
            
            # 記錄操作日誌
            self.db.log_action(
                user_id=user_id,
                action='create_product',
                entity_type='products',
                entity_id=product_id,
                details={'product_data': product_data}
            )
            
            return product_id
        
        except Exception as e:
            logging.error(f"添加產品時發生錯誤: {str(e)}")
            return None
    
    def update_product(self, user_id: int, product_id: int, product_data: Dict[str, Any]) -> bool:
        """
        更新產品
        
        Args:
            user_id: 用户ID
            product_id: 產品ID
            product_data: 要更新的產品數據
        
        Returns:
            是否更新成功
        """
        try:
            # 檢查產品是否存在且屬於該用户
            existing = self.get_product(user_id, product_id)
            if not existing:
                logging.error(f"更新產品失敗: 產品不存在或不屬於該用户 (user_id: {user_id}, product_id: {product_id})")
                return False
            
            # 檢查產品代碼是否已存在（如果要更新產品代碼）
            if 'product_code' in product_data and product_data['product_code'] != existing['product_code']:
                check = self.db.fetch_one(
                    "SELECT * FROM products WHERE product_code = ? AND user_id = ? AND product_id != ?",
                    (product_data['product_code'], user_id, product_id)
                )
                
                if check:
                    logging.error(f"更新產品失敗: 產品代碼已存在 (product_code: {product_data['product_code']})")
                    return False
            
            # 添加更新時間
            if 'updated_at' not in product_data:
                product_data['updated_at'] = int(time.time())
            
            # 更新產品
            rows_affected = self.db.update(
                'products',
                product_data,
                "product_id = ? AND user_id = ?",
                (product_id, user_id)
            )
            
            # 記錄操作日誌
            self.db.log_action(
                user_id=user_id,
                action='update_product',
                entity_type='products',
                entity_id=product_id,
                details={'product_data': product_data}
            )
            
            return rows_affected > 0
        
        except Exception as e:
            logging.error(f"更新產品時發生錯誤: {str(e)}")
            return False
    
    def delete_product(self, user_id: int, product_id: int) -> bool:
        """
        刪除產品
        
        Args:
            user_id: 用户ID
            product_id: 產品ID
        
        Returns:
            是否刪除成功
        """
        try:
            # 開始事務
            self.db.begin_transaction()
            
            # 檢查產品是否存在且屬於該用户
            existing = self.get_product(user_id, product_id)
            if not existing:
                logging.error(f"刪除產品失敗: 產品不存在或不屬於該用户 (user_id: {user_id}, product_id: {product_id})")
                self.db.rollback_transaction()
                return False
            
            # 檢查產品是否已被使用
            used = self.db.fetch_one(
                "SELECT COUNT(*) as count FROM shipment_items WHERE product_id = ?",
                (product_id,)
            )
            
            if used and used['count'] > 0:
                logging.error(f"刪除產品失敗: 產品已被使用 (product_id: {product_id}, count: {used['count']})")
                self.db.rollback_transaction()
                return False
            
            # 刪除產品
            rows_affected = self.db.delete('products', "product_id = ? AND user_id = ?", (product_id, user_id))
            
            # 記錄操作日誌
            self.db.log_action(
                user_id=user_id,
                action='delete_product',
                entity_type='products',
                entity_id=product_id
            )
            
            # 提交事務
            self.db.commit_transaction()
            
            return rows_affected > 0
        
        except Exception as e:
            # 回滾事務
            self.db.rollback_transaction()
            logging.error(f"刪除產品時發生錯誤: {str(e)}")
            return False
    
    def get_product(self, user_id: int, product_id: int) -> Optional[Dict[str, Any]]:
        """
        獲取產品詳情
        
        Args:
            user_id: 用户ID
            product_id: 產品ID
        
        Returns:
            產品詳情，如果不存在或不屬於該用户則返回None
        """
        try:
            # 獲取產品
            product = self.db.fetch_one(
                "SELECT * FROM products WHERE product_id = ? AND user_id = ?",
                (product_id, user_id)
            )
            
            return product
        
        except Exception as e:
            logging.error(f"獲取產品時發生錯誤: {str(e)}")
            return None
    
    def get_products(self, user_id: int, filters: Dict[str, Any] = None, limit: int = 100, offset: int = 0,
                    order_by: str = "product_name ASC") -> Tuple[List[Dict[str, Any]], int]:
        """
        獲取產品列表
        
        Args:
            user_id: 用户ID
            filters: 過濾條件
            limit: 最大返回結果數
            offset: 結果偏移量
            order_by: 結果排序方式
        
        Returns:
            (產品列表, 總記錄數)
        """
        try:
            # 構建查詢條件
            conditions = ["user_id = ?"]
            params = [user_id]
            
            if filters:
                # 產品名稱過濾
                if 'product_name' in filters and filters['product_name']:
                    conditions.append("product_name LIKE ?")
                    params.append(f"%{filters['product_name']}%")
                
                # 產品代碼過濾
                if 'product_code' in filters and filters['product_code']:
                    conditions.append("product_code LIKE ?")
                    params.append(f"%{filters['product_code']}%")
                
                # 產品分類過濾
                if 'category' in filters and filters['category']:
                    conditions.append("category = ?")
                    params.append(filters['category'])
            
            # 構建WHERE子句
            where_clause = " AND ".join(conditions)
            
            # 查詢總記錄數
            count_query = f"SELECT COUNT(*) as total FROM products WHERE {where_clause}"
            count_result = self.db.fetch_one(count_query, tuple(params))
            total = count_result['total'] if count_result else 0
            
            # 查詢產品列表
            products_query = f"""
                SELECT * FROM products 
                WHERE {where_clause}
                ORDER BY {order_by}
                LIMIT ? OFFSET ?
            """
            
            # 添加分頁參數
            params.extend([limit, offset])
            
            products = self.db.fetch_all(products_query, tuple(params))
            
            return products, total
        
        except Exception as e:
            logging.error(f"獲取產品列表時發生錯誤: {str(e)}")
            return [], 0
    
    def get_product_by_code(self, user_id: int, product_code: str) -> Optional[Dict[str, Any]]:
        """
        根據產品代碼獲取產品
        
        Args:
            user_id: 用户ID
            product_code: 產品代碼
        
        Returns:
            產品詳情，如果不存在或不屬於該用户則返回None
        """
        try:
            # 獲取產品
            product = self.db.fetch_one(
                "SELECT * FROM products WHERE product_code = ? AND user_id = ?",
                (product_code, user_id)
            )
            
            return product
        
        except Exception as e:
            logging.error(f"根據產品代碼獲取產品時發生錯誤: {str(e)}")
            return None
    
    def search_products(self, user_id: int, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索產品
        
        Args:
            user_id: 用户ID
            keyword: 搜索關鍵詞
            limit: 最大返回結果數
        
        Returns:
            產品列表
        """
        try:
            # 構建查詢條件
            query = """
                SELECT * FROM products 
                WHERE user_id = ? AND (
                    product_name LIKE ? OR 
                    product_code LIKE ? OR 
                    description LIKE ?
                )
                ORDER BY product_name ASC
                LIMIT ?
            """
            
            # 執行查詢
            keyword_param = f"%{keyword}%"
            products = self.db.fetch_all(
                query,
                (user_id, keyword_param, keyword_param, keyword_param, limit)
            )
            
            return products
        
        except Exception as e:
            logging.error(f"搜索產品時發生錯誤: {str(e)}")
            return []
    
    def get_product_categories(self, user_id: int) -> List[str]:
        """
        獲取產品分類列表
        
        Args:
            user_id: 用户ID
        
        Returns:
            產品分類列表
        """
        try:
            # 執行查詢
            query = """
                SELECT DISTINCT category FROM products 
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                ORDER BY category ASC
            """
            
            result = self.db.fetch_all(query, (user_id,))
            
            # 提取分類名稱
            categories = [row['category'] for row in result if row['category']]
            
            return categories
        
        except Exception as e:
            logging.error(f"獲取產品分類列表時發生錯誤: {str(e)}")
            return []
    
    def get_product_stats(self, user_id: int) -> Dict[str, Any]:
        """
        獲取產品統計數據
        
        Args:
            user_id: 用户ID
        
        Returns:
            統計數據
        """
        try:
            # 獲取總產品數
            count_query = "SELECT COUNT(*) as total FROM products WHERE user_id = ?"
            count_result = self.db.fetch_one(count_query, (user_id,))
            total_products = count_result['total'] if count_result else 0
            
            # 獲取各分類產品數
            category_query = """
                SELECT category, COUNT(*) as count 
                FROM products 
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                GROUP BY category
                ORDER BY count DESC
            """
            category_results = self.db.fetch_all(category_query, (user_id,))
            category_counts = {result['category']: result['count'] for result in category_results}
            
            # 獲取最近添加的產品
            recent_query = """
                SELECT * FROM products 
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 5
            """
            recent_products = self.db.fetch_all(recent_query, (user_id,))
            
            # 獲取最常用產品（根據出貨記錄）
            popular_query = """
                SELECT p.*, COUNT(si.item_id) as usage_count
                FROM products p
                JOIN shipment_items si ON p.product_id = si.product_id
                JOIN shipments s ON si.shipment_id = s.shipment_id
                WHERE p.user_id = ? AND s.user_id = ?
                GROUP BY p.product_id
                ORDER BY usage_count DESC
                LIMIT 5
            """
            popular_products = self.db.fetch_all(popular_query, (user_id, user_id))
            
            # 組合統計數據
            stats = {
                'total_products': total_products,
                'category_counts': category_counts,
                'recent_products': recent_products,
                'popular_products': popular_products
            }
            
            return stats
        
        except Exception as e:
            logging.error(f"獲取產品統計數據時發生錯誤: {str(e)}")
            return {
                'total_products': 0,
                'category_counts': {},
                'recent_products': [],
                'popular_products': []
            }


# 單例模式，確保只有一個實例
_product_db_instance = None

def get_product_db() -> ProductDB:
    """
    獲取產品數據庫操作實例
    
    Returns:
        產品數據庫操作實例
    """
    global _product_db_instance
    
    if _product_db_instance is None:
        _product_db_instance = ProductDB()
    
    return _product_db_instance


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    import sys
    sys.exit(1) 