#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 數據庫連接模塊

此模塊提供與數據庫的連接和基本操作。
"""

import logging
import os
import sqlite3
from typing import Dict, List, Any, Optional, Tuple, Union
import time
import json
from datetime import datetime


class Database:
    """數據庫連接和操作類"""
    
    def __init__(self, db_path: str = None):
        """
        初始化數據庫連接
        
        Args:
            db_path: 數據庫文件路徑，如果為None則使用默認路徑
        """
        if db_path is None:
            # 默認路徑為應用根目錄下的database目錄
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            db_dir = os.path.join(root_dir, 'database')
            
            # 確保數據庫目錄存在
            if not os.path.exists(db_dir):
                os.makedirs(db_dir)
            
            db_path = os.path.join(db_dir, 'database.sqlite')
        
        # 存儲數據庫路徑
        self.db_path = db_path
        self.connection = None
        self.connected = False
        
        # 嘗試連接數據庫
        self.connect()
    
    def connect(self) -> bool:
        """
        連接到數據庫
        
        Returns:
            是否連接成功
        """
        try:
            self.connection = sqlite3.connect(self.db_path)
            
            # 設置行工廠為字典，以便查詢結果可以通過列名訪問
            self.connection.row_factory = sqlite3.Row
            
            # 啟用外鍵約束
            self.connection.execute("PRAGMA foreign_keys = ON")
            
            # 設置超時
            self.connection.execute("PRAGMA busy_timeout = 5000")
            
            self.connected = True
            logging.info(f"成功連接到數據庫: {self.db_path}")
            
            # 初始化數據庫結構
            self._init_db()
            
            return True
        
        except Exception as e:
            self.connected = False
            logging.error(f"連接數據庫時發生錯誤: {str(e)}")
            return False
    
    def disconnect(self) -> bool:
        """
        斷開數據庫連接
        
        Returns:
            是否斷開成功
        """
        if self.connection:
            try:
                self.connection.close()
                self.connected = False
                logging.info("成功斷開數據庫連接")
                return True
            
            except Exception as e:
                logging.error(f"斷開數據庫連接時發生錯誤: {str(e)}")
        
        return False
    
    def reconnect(self) -> bool:
        """
        重新連接到數據庫
        
        Returns:
            是否重新連接成功
        """
        self.disconnect()
        return self.connect()
    
    def _init_db(self) -> None:
        """
        初始化數據庫結構（如果不存在）
        """
        try:
            # 創建用户表
            self.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    email TEXT,
                    role TEXT NOT NULL DEFAULT 'user',
                    full_name TEXT,
                    company TEXT,
                    phone TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # 創建訂閱表
            self.execute('''
                CREATE TABLE IF NOT EXISTS subscriptions (
                    subscription_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    start_date TIMESTAMP NOT NULL,
                    end_date TIMESTAMP NOT NULL,
                    payment_method TEXT,
                    payment_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建設備表
            self.execute('''
                CREATE TABLE IF NOT EXISTS devices (
                    device_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_uuid TEXT NOT NULL UNIQUE,
                    user_id INTEGER,
                    device_name TEXT,
                    device_info TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建出貨記錄表
            self.execute('''
                CREATE TABLE IF NOT EXISTS shipments (
                    shipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    shipment_date TIMESTAMP NOT NULL,
                    customer_name TEXT,
                    customer_address TEXT,
                    total_amount REAL NOT NULL,
                    status TEXT NOT NULL DEFAULT 'pending',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建出貨商品表
            self.execute('''
                CREATE TABLE IF NOT EXISTS shipment_items (
                    item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shipment_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    price REAL NOT NULL,
                    subtotal REAL NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (shipment_id) REFERENCES shipments(shipment_id)
                )
            ''')
            
            # 創建商品表
            self.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    product_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    product_name TEXT NOT NULL,
                    product_code TEXT,
                    product_category TEXT,
                    price REAL NOT NULL DEFAULT 0,
                    unit TEXT,
                    description TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建客戶表
            self.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    customer_name TEXT NOT NULL,
                    contact_person TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    notes TEXT,
                    is_active INTEGER NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建日誌表
            self.execute('''
                CREATE TABLE IF NOT EXISTS logs (
                    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    entity_type TEXT,
                    entity_id INTEGER,
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建設置表
            self.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id),
                    UNIQUE(user_id, setting_key)
                )
            ''')
            
            # 創建密碼重置表
            self.execute('''
                CREATE TABLE IF NOT EXISTS password_resets (
                    reset_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    reset_token TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    used INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            
            # 創建初始管理員帳戶（如果不存在）
            self._create_default_admin()
            
            logging.info("數據庫結構初始化完成")
        
        except Exception as e:
            logging.error(f"初始化數據庫結構時發生錯誤: {str(e)}")
    
    def _create_default_admin(self) -> None:
        """
        創建初始管理員帳戶（如果不存在）
        """
        try:
            # 檢查是否存在管理員帳戶
            admin = self.fetch_one("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1")
            
            if not admin:
                # 創建默認管理員帳戶
                from hashlib import sha256
                
                username = "admin"
                password = "admin123"  # 默認密碼，建議用戶第一次登錄後修改
                hashed_password = sha256(password.encode()).hexdigest()
                
                self.execute(
                    "INSERT INTO users (username, password, email, role, full_name) VALUES (?, ?, ?, ?, ?)",
                    (username, hashed_password, "<EMAIL>", "admin", "系統管理員")
                )
                
                logging.info("已創建默認管理員帳戶")
        
        except Exception as e:
            logging.error(f"創建默認管理員帳戶時發生錯誤: {str(e)}")
    
    def execute(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """
        執行SQL查詢
        
        Args:
            query: SQL查詢語句
            params: 查詢參數
        
        Returns:
            查詢結果游標
        
        Raises:
            Exception: 執行查詢時發生錯誤
        """
        if not self.connected:
            self.connect()
        
        cursor = None
        max_retries = 3
        retry_count = 0
        last_error = None
        
        while retry_count < max_retries:
            try:
                cursor = self.connection.cursor()
                cursor.execute(query, params)
                self.connection.commit()
                return cursor
            
            except sqlite3.OperationalError as e:
                # 數據庫鎖定錯誤，嘗試重試
                if "database is locked" in str(e):
                    retry_count += 1
                    last_error = e
                    logging.warning(f"數據庫鎖定，正在重試 ({retry_count}/{max_retries})...")
                    time.sleep(0.5)  # 等待一段時間再重試
                else:
                    raise
            
            except sqlite3.Error as e:
                # 其他SQLite錯誤
                logging.error(f"執行查詢時發生SQLite錯誤: {str(e)}\nSQL: {query}\n參數: {params}")
                raise
            
            except Exception as e:
                # 其他錯誤
                logging.error(f"執行查詢時發生錯誤: {str(e)}\nSQL: {query}\n參數: {params}")
                raise
        
        # 重試次數用完仍未成功
        if last_error:
            logging.error(f"執行查詢失敗，已超過最大重試次數: {str(last_error)}\nSQL: {query}\n參數: {params}")
            raise last_error
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """
        執行查詢並獲取第一行結果
        
        Args:
            query: SQL查詢語句
            params: 查詢參數
        
        Returns:
            查詢結果字典，如果沒有結果則返回None
        """
        cursor = self.execute(query, params)
        row = cursor.fetchone()
        
        if row:
            return dict(row)
        return None
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        執行查詢並獲取所有結果
        
        Args:
            query: SQL查詢語句
            params: 查詢參數
        
        Returns:
            查詢結果字典列表
        """
        cursor = self.execute(query, params)
        rows = cursor.fetchall()
        
        return [dict(row) for row in rows]
    
    def insert(self, table: str, data: Dict[str, Any]) -> int:
        """
        向表中插入數據
        
        Args:
            table: 表名
            data: 要插入的數據字典
        
        Returns:
            新插入記錄的ID
        """
        # 過濾掉值為None的項
        filtered_data = {k: v for k, v in data.items() if v is not None}
        
        # 添加更新時間
        filtered_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        columns = ', '.join(filtered_data.keys())
        placeholders = ', '.join(['?'] * len(filtered_data))
        values = tuple(filtered_data.values())
        
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        cursor = self.execute(query, values)
        return cursor.lastrowid
    
    def update(self, table: str, data: Dict[str, Any], condition: str, params: tuple = ()) -> int:
        """
        更新表中的數據
        
        Args:
            table: 表名
            data: 要更新的數據字典
            condition: WHERE條件
            params: 條件參數
        
        Returns:
            受影響的行數
        """
        # 過濾掉值為None的項
        filtered_data = {k: v for k, v in data.items() if v is not None}
        
        # 添加更新時間
        filtered_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 構建SET子句
        set_clause = ', '.join([f"{column} = ?" for column in filtered_data.keys()])
        values = tuple(filtered_data.values())
        
        query = f"UPDATE {table} SET {set_clause} WHERE {condition}"
        
        # 合併參數
        all_params = values + params
        
        cursor = self.execute(query, all_params)
        return cursor.rowcount
    
    def delete(self, table: str, condition: str, params: tuple = ()) -> int:
        """
        從表中刪除數據
        
        Args:
            table: 表名
            condition: WHERE條件
            params: 條件參數
        
        Returns:
            受影響的行數
        """
        query = f"DELETE FROM {table} WHERE {condition}"
        
        cursor = self.execute(query, params)
        return cursor.rowcount
    
    def transaction(self) -> sqlite3.Connection:
        """
        開始一個事務
        
        Returns:
            數據庫連接，用於提交或回滾事務
        """
        return self.connection
    
    def begin_transaction(self) -> None:
        """
        顯式開始一個事務
        """
        if not self.connected:
            self.connect()
        self.connection.execute("BEGIN TRANSACTION")
    
    def commit_transaction(self) -> None:
        """
        提交當前事務
        """
        if self.connected:
            self.connection.commit()
    
    def rollback_transaction(self) -> None:
        """
        回滾當前事務
        """
        if self.connected:
            self.connection.rollback()
    
    def log_action(self, user_id: Optional[int], action: str, entity_type: Optional[str] = None,
                  entity_id: Optional[int] = None, details: Optional[Dict[str, Any]] = None) -> int:
        """
        記錄用户操作
        
        Args:
            user_id: 用户ID，如果為None則表示系統操作
            action: 操作類型
            entity_type: 實體類型
            entity_id: 實體ID
            details: 操作詳情
        
        Returns:
            日誌記錄ID
        """
        log_data = {
            'user_id': user_id,
            'action': action,
            'entity_type': entity_type,
            'entity_id': entity_id,
            'details': json.dumps(details) if details else None
        }
        
        return self.insert('logs', log_data)
    
    def get_logs(self, filters: Dict[str, Any] = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        獲取操作日誌
        
        Args:
            filters: 過濾條件
            limit: 最大返回結果數
            offset: 結果偏移量
        
        Returns:
            日誌記錄列表
        """
        query = "SELECT * FROM logs"
        params = []
        
        # 添加過濾條件
        if filters:
            conditions = []
            for key, value in filters.items():
                if value is not None:
                    conditions.append(f"{key} = ?")
                    params.append(value)
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
        
        # 添加排序和分頁
        query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        return self.fetch_all(query, tuple(params))
    
    def get_setting(self, key: str, user_id: Optional[int] = None) -> Optional[str]:
        """
        獲取設置值
        
        Args:
            key: 設置鍵
            user_id: 用户ID，如果為None則表示全局設置
        
        Returns:
            設置值，如果不存在則返回None
        """
        if user_id:
            result = self.fetch_one(
                "SELECT setting_value FROM settings WHERE setting_key = ? AND user_id = ?",
                (key, user_id)
            )
        else:
            result = self.fetch_one(
                "SELECT setting_value FROM settings WHERE setting_key = ? AND user_id IS NULL",
                (key,)
            )
        
        return result['setting_value'] if result else None
    
    def set_setting(self, key: str, value: str, user_id: Optional[int] = None) -> bool:
        """
        設置設置值
        
        Args:
            key: 設置鍵
            value: 設置值
            user_id: 用户ID，如果為None則表示全局設置
        
        Returns:
            是否成功
        """
        try:
            # 檢查設置是否已存在
            if user_id:
                existing = self.fetch_one(
                    "SELECT setting_id FROM settings WHERE setting_key = ? AND user_id = ?",
                    (key, user_id)
                )
            else:
                existing = self.fetch_one(
                    "SELECT setting_id FROM settings WHERE setting_key = ? AND user_id IS NULL",
                    (key,)
                )
            
            if existing:
                # 更新現有設置
                if user_id:
                    self.update(
                        'settings',
                        {'setting_value': value},
                        "setting_key = ? AND user_id = ?",
                        (key, user_id)
                    )
                else:
                    self.update(
                        'settings',
                        {'setting_value': value},
                        "setting_key = ? AND user_id IS NULL",
                        (key,)
                    )
            else:
                # 插入新設置
                self.insert(
                    'settings',
                    {'setting_key': key, 'setting_value': value, 'user_id': user_id}
                )
            
            return True
        
        except Exception as e:
            logging.error(f"設置設置值時發生錯誤: {str(e)}")
            return False
    
    def get_db_size(self) -> int:
        """
        獲取數據庫文件大小
        
        Returns:
            數據庫大小（字節）
        """
        try:
            return os.path.getsize(self.db_path)
        except Exception as e:
            logging.error(f"獲取數據庫大小時發生錯誤: {str(e)}")
            return 0
    
    def backup_db(self, backup_path: Optional[str] = None) -> Optional[str]:
        """
        備份數據庫
        
        Args:
            backup_path: 備份文件路徑，如果為None則使用默認路徑
        
        Returns:
            備份文件路徑，如果備份失敗則返回None
        """
        try:
            # 如果未提供備份路徑，則生成默認路徑
            if not backup_path:
                db_dir = os.path.dirname(self.db_path)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = os.path.join(db_dir, f'backup_{timestamp}.sqlite')
            
            # 確保備份目錄存在
            backup_dir = os.path.dirname(backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # 創建備份
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            logging.info(f"數據庫成功備份到: {backup_path}")
            return backup_path
        
        except Exception as e:
            logging.error(f"備份數據庫時發生錯誤: {str(e)}")
            return None
    
    def __del__(self):
        """
        析構函數，確保數據庫連接被正確關閉
        """
        self.disconnect()


# 單例模式，確保只有一個數據庫連接實例
_db_instance = None

def get_db_instance() -> Database:
    """
    獲取數據庫實例
    
    Returns:
        數據庫實例
    """
    global _db_instance
    
    if _db_instance is None:
        _db_instance = Database()
    
    return _db_instance


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    import sys
    sys.exit(1) 