#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 進行出貨界面

此模塊實現進行出貨的用戶界面，包括產品選擇、數量輸入、出貨確認等功能。
"""

import os
import sys
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QTextEdit, QGroupBox, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame, QDateEdit,
    QCheckBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon


class ProcessShipmentWidget(QWidget):
    """
    進行出貨界面類
    """
    
    # 自定義信號
    shipment_completed = Signal(dict)  # 出貨完成信號
    
    def __init__(self, user_info: Dict[str, Any], env_info: Dict[str, Any]):
        """
        初始化進行出貨界面
        
        Args:
            user_info: 當前登入用戶的信息
            env_info: 環境信息，包括數據目錄、調試模式等
        """
        super().__init__()
        
        self.user_info = user_info
        self.env_info = env_info
        
        self._setup_ui()
        self._connect_signals()
        self._load_initial_data()
    
    def _setup_ui(self):
        """設置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 標題
        title_label = QLabel("進行出貨")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 出貨信息區域
        shipment_info_group = QGroupBox("出貨信息")
        shipment_info_layout = QGridLayout(shipment_info_group)
        
        # 出貨日期
        shipment_info_layout.addWidget(QLabel("出貨日期:"), 0, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        shipment_info_layout.addWidget(self.date_edit, 0, 1)
        
        # 客戶名稱
        shipment_info_layout.addWidget(QLabel("客戶名稱:"), 0, 2)
        self.customer_edit = QLineEdit()
        self.customer_edit.setPlaceholderText("請輸入客戶名稱")
        shipment_info_layout.addWidget(self.customer_edit, 0, 3)
        
        # 出貨單號
        shipment_info_layout.addWidget(QLabel("出貨單號:"), 1, 0)
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setPlaceholderText("自動生成或手動輸入")
        shipment_info_layout.addWidget(self.shipment_number_edit, 1, 1)
        
        # 生成出貨單號按鈕
        self.generate_number_btn = QPushButton("自動生成")
        self.generate_number_btn.setMaximumWidth(100)
        shipment_info_layout.addWidget(self.generate_number_btn, 1, 2)
        
        # 備註
        shipment_info_layout.addWidget(QLabel("備註:"), 2, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("請輸入備註信息")
        shipment_info_layout.addWidget(self.notes_edit, 2, 1, 1, 3)
        
        main_layout.addWidget(shipment_info_group)
        
        # 產品選擇區域
        product_group = QGroupBox("產品選擇")
        product_layout = QVBoxLayout(product_group)
        
        # 產品選擇控件
        product_select_layout = QHBoxLayout()
        
        product_select_layout.addWidget(QLabel("產品:"))
        self.product_combo = QComboBox()
        self.product_combo.setMinimumWidth(200)
        product_select_layout.addWidget(self.product_combo)
        
        product_select_layout.addWidget(QLabel("數量:"))
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        product_select_layout.addWidget(self.quantity_spin)
        
        self.add_product_btn = QPushButton("添加產品")
        self.add_product_btn.setMaximumWidth(100)
        product_select_layout.addWidget(self.add_product_btn)
        
        product_select_layout.addStretch()
        product_layout.addLayout(product_select_layout)
        
        # 產品列表表格
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "產品名稱", "產品編號", "數量", "單價", "操作"
        ])
        
        # 設置表格樣式
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        product_layout.addWidget(self.products_table)
        main_layout.addWidget(product_group)
        
        # 操作按鈕區域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.clear_btn = QPushButton("清空")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        button_layout.addWidget(self.clear_btn)
        
        self.save_draft_btn = QPushButton("保存草稿")
        self.save_draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        button_layout.addWidget(self.save_draft_btn)
        
        self.confirm_shipment_btn = QPushButton("確認出貨")
        self.confirm_shipment_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.confirm_shipment_btn)
        
        main_layout.addLayout(button_layout)
        
        # 進度條（初始隱藏）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def _connect_signals(self):
        """連接信號和槽"""
        self.generate_number_btn.clicked.connect(self._generate_shipment_number)
        self.add_product_btn.clicked.connect(self._add_product)
        self.clear_btn.clicked.connect(self._clear_form)
        self.save_draft_btn.clicked.connect(self._save_draft)
        self.confirm_shipment_btn.clicked.connect(self._confirm_shipment)
    
    def _load_initial_data(self):
        """加載初始數據"""
        # 加載產品列表
        self._load_products()
        
        # 生成初始出貨單號
        self._generate_shipment_number()
    
    def _load_products(self):
        """加載產品列表"""
        # 這裡應該從數據庫加載產品列表
        # 暫時使用示例數據
        sample_products = [
            "產品A - 型號001",
            "產品B - 型號002", 
            "產品C - 型號003",
            "產品D - 型號004"
        ]
        
        self.product_combo.addItems(sample_products)
    
    def _generate_shipment_number(self):
        """生成出貨單號"""
        # 生成格式: SH + 年月日 + 時分秒
        now = datetime.now()
        shipment_number = f"SH{now.strftime('%Y%m%d%H%M%S')}"
        self.shipment_number_edit.setText(shipment_number)
    
    def _add_product(self):
        """添加產品到列表"""
        if not self.product_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇產品")
            return
        
        product_name = self.product_combo.currentText()
        quantity = self.quantity_spin.value()
        
        # 添加到表格
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        self.products_table.setItem(row, 0, QTableWidgetItem(product_name))
        self.products_table.setItem(row, 1, QTableWidgetItem("PRD001"))  # 示例產品編號
        self.products_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
        self.products_table.setItem(row, 3, QTableWidgetItem("100.00"))  # 示例單價
        
        # 添加刪除按鈕
        delete_btn = QPushButton("刪除")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(lambda: self._remove_product(row))
        self.products_table.setCellWidget(row, 4, delete_btn)
    
    def _remove_product(self, row):
        """移除產品"""
        self.products_table.removeRow(row)
    
    def _clear_form(self):
        """清空表單"""
        reply = QMessageBox.question(
            self, "確認清空", "確定要清空所有內容嗎？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.customer_edit.clear()
            self.notes_edit.clear()
            self.products_table.setRowCount(0)
            self._generate_shipment_number()
    
    def _save_draft(self):
        """保存草稿"""
        # 這裡應該實現保存草稿的邏輯
        QMessageBox.information(self, "提示", "草稿已保存")
    
    def _confirm_shipment(self):
        """確認出貨"""
        # 驗證必填字段
        if not self.customer_edit.text().strip():
            QMessageBox.warning(self, "警告", "請輸入客戶名稱")
            return
        
        if not self.shipment_number_edit.text().strip():
            QMessageBox.warning(self, "警告", "請輸入出貨單號")
            return
        
        if self.products_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "請至少添加一個產品")
            return
        
        # 確認對話框
        reply = QMessageBox.question(
            self, "確認出貨", "確定要進行出貨嗎？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._process_shipment()
    
    def _process_shipment(self):
        """處理出貨"""
        # 顯示進度條
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 模擬處理過程
        self.confirm_shipment_btn.setEnabled(False)
        
        # 這裡應該實現實際的出貨處理邏輯
        # 暫時使用定時器模擬
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_progress)
        self.progress = 0
        self.timer.start(100)
    
    def _update_progress(self):
        """更新進度"""
        self.progress += 10
        self.progress_bar.setValue(self.progress)
        
        if self.progress >= 100:
            self.timer.stop()
            self.progress_bar.setVisible(False)
            self.confirm_shipment_btn.setEnabled(True)
            
            # 出貨完成
            shipment_data = {
                'shipment_number': self.shipment_number_edit.text(),
                'customer': self.customer_edit.text(),
                'date': self.date_edit.date().toString('yyyy-MM-dd'),
                'notes': self.notes_edit.toPlainText(),
                'products': self._get_products_data()
            }
            
            self.shipment_completed.emit(shipment_data)
            QMessageBox.information(self, "成功", "出貨完成！")
            self._clear_form()
    
    def _get_products_data(self):
        """獲取產品數據"""
        products = []
        for row in range(self.products_table.rowCount()):
            product = {
                'name': self.products_table.item(row, 0).text(),
                'code': self.products_table.item(row, 1).text(),
                'quantity': int(self.products_table.item(row, 2).text()),
                'price': float(self.products_table.item(row, 3).text())
            }
            products.append(product)
        return products


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1)
