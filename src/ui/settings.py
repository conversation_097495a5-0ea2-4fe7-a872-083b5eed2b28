#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 系統設置界面

此模塊實現系統設置的用戶界面，包括用戶管理、系統配置、數據備份等功能。
"""

import os
import sys
import logging
from typing import Dict, Any, List, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QTextEdit, QGroupBox, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QFrame, QCheckBox,
    QTabWidget, QFileDialog, QProgressBar, QSlider
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon


class SettingsWidget(QWidget):
    """
    系統設置界面類
    """
    
    # 自定義信號
    settings_changed = Signal(dict)  # 設置變更信號
    
    def __init__(self, user_info: Dict[str, Any], env_info: Dict[str, Any]):
        """
        初始化系統設置界面
        
        Args:
            user_info: 當前登入用戶的信息
            env_info: 環境信息，包括數據目錄、調試模式等
        """
        super().__init__()
        
        self.user_info = user_info
        self.env_info = env_info
        self.is_admin = user_info.get('role') == 'admin'
        
        self._setup_ui()
        self._connect_signals()
        self._load_settings()
    
    def _setup_ui(self):
        """設置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 標題
        title_label = QLabel("系統設置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 標籤頁
        self.tab_widget = QTabWidget()
        
        # 個人設置標籤
        self._create_personal_tab()
        
        # 系統設置標籤（僅管理員可見）
        if self.is_admin:
            self._create_system_tab()
            self._create_user_management_tab()
            self._create_backup_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # 底部按鈕
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存設置")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.save_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        button_layout.addWidget(self.reset_btn)
        
        main_layout.addLayout(button_layout)
    
    def _create_personal_tab(self):
        """創建個人設置標籤"""
        personal_widget = QWidget()
        layout = QVBoxLayout(personal_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 個人信息組
        personal_group = QGroupBox("個人信息")
        personal_layout = QGridLayout(personal_group)
        
        personal_layout.addWidget(QLabel("用戶名:"), 0, 0)
        self.username_edit = QLineEdit(self.user_info.get('username', ''))
        self.username_edit.setReadOnly(True)
        personal_layout.addWidget(self.username_edit, 0, 1)
        
        personal_layout.addWidget(QLabel("全名:"), 1, 0)
        self.fullname_edit = QLineEdit(self.user_info.get('full_name', ''))
        personal_layout.addWidget(self.fullname_edit, 1, 1)
        
        personal_layout.addWidget(QLabel("電子郵件:"), 2, 0)
        self.email_edit = QLineEdit(self.user_info.get('email', ''))
        personal_layout.addWidget(self.email_edit, 2, 1)
        
        layout.addWidget(personal_group)
        
        # 密碼修改組
        password_group = QGroupBox("修改密碼")
        password_layout = QGridLayout(password_group)
        
        password_layout.addWidget(QLabel("當前密碼:"), 0, 0)
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(self.current_password_edit, 0, 1)
        
        password_layout.addWidget(QLabel("新密碼:"), 1, 0)
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(self.new_password_edit, 1, 1)
        
        password_layout.addWidget(QLabel("確認新密碼:"), 2, 0)
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(self.confirm_password_edit, 2, 1)
        
        self.change_password_btn = QPushButton("修改密碼")
        self.change_password_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        password_layout.addWidget(self.change_password_btn, 3, 1)
        
        layout.addWidget(password_group)
        layout.addStretch()
        
        self.tab_widget.addTab(personal_widget, "個人設置")
    
    def _create_system_tab(self):
        """創建系統設置標籤"""
        system_widget = QWidget()
        layout = QVBoxLayout(system_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 應用設置組
        app_group = QGroupBox("應用設置")
        app_layout = QGridLayout(app_group)
        
        app_layout.addWidget(QLabel("應用名稱:"), 0, 0)
        self.app_name_edit = QLineEdit("出貨記錄工具")
        app_layout.addWidget(self.app_name_edit, 0, 1)
        
        app_layout.addWidget(QLabel("公司名稱:"), 1, 0)
        self.company_name_edit = QLineEdit("示例公司")
        app_layout.addWidget(self.company_name_edit, 1, 1)
        
        app_layout.addWidget(QLabel("自動保存間隔(分鐘):"), 2, 0)
        self.autosave_spin = QSpinBox()
        self.autosave_spin.setMinimum(1)
        self.autosave_spin.setMaximum(60)
        self.autosave_spin.setValue(5)
        app_layout.addWidget(self.autosave_spin, 2, 1)
        
        layout.addWidget(app_group)
        
        # 數據庫設置組
        db_group = QGroupBox("數據庫設置")
        db_layout = QGridLayout(db_group)
        
        db_layout.addWidget(QLabel("數據庫路徑:"), 0, 0)
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setReadOnly(True)
        db_layout.addWidget(self.db_path_edit, 0, 1)
        
        self.browse_db_btn = QPushButton("瀏覽")
        db_layout.addWidget(self.browse_db_btn, 0, 2)
        
        db_layout.addWidget(QLabel("自動備份:"), 1, 0)
        self.auto_backup_check = QCheckBox("啟用自動備份")
        self.auto_backup_check.setChecked(True)
        db_layout.addWidget(self.auto_backup_check, 1, 1)
        
        layout.addWidget(db_group)
        
        # 日誌設置組
        log_group = QGroupBox("日誌設置")
        log_layout = QGridLayout(log_group)
        
        log_layout.addWidget(QLabel("日誌級別:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.setCurrentText("INFO")
        log_layout.addWidget(self.log_level_combo, 0, 1)
        
        log_layout.addWidget(QLabel("保留天數:"), 1, 0)
        self.log_retention_spin = QSpinBox()
        self.log_retention_spin.setMinimum(1)
        self.log_retention_spin.setMaximum(365)
        self.log_retention_spin.setValue(30)
        log_layout.addWidget(self.log_retention_spin, 1, 1)
        
        layout.addWidget(log_group)
        layout.addStretch()
        
        self.tab_widget.addTab(system_widget, "系統設置")
    
    def _create_user_management_tab(self):
        """創建用戶管理標籤"""
        user_widget = QWidget()
        layout = QVBoxLayout(user_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 用戶列表
        user_group = QGroupBox("用戶管理")
        user_layout = QVBoxLayout(user_group)
        
        # 操作按鈕
        button_layout = QHBoxLayout()
        self.add_user_btn = QPushButton("添加用戶")
        self.add_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.add_user_btn)
        
        self.edit_user_btn = QPushButton("編輯用戶")
        self.edit_user_btn.setEnabled(False)
        button_layout.addWidget(self.edit_user_btn)
        
        self.delete_user_btn = QPushButton("刪除用戶")
        self.delete_user_btn.setEnabled(False)
        self.delete_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.delete_user_btn)
        
        button_layout.addStretch()
        user_layout.addLayout(button_layout)
        
        # 用戶表格
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels([
            "用戶名", "全名", "電子郵件", "角色", "狀態"
        ])
        
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        user_layout.addWidget(self.users_table)
        layout.addWidget(user_group)
        
        self.tab_widget.addTab(user_widget, "用戶管理")
    
    def _create_backup_tab(self):
        """創建備份標籤"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 備份設置組
        backup_group = QGroupBox("備份設置")
        backup_layout = QGridLayout(backup_group)
        
        backup_layout.addWidget(QLabel("備份路徑:"), 0, 0)
        self.backup_path_edit = QLineEdit()
        backup_layout.addWidget(self.backup_path_edit, 0, 1)
        
        self.browse_backup_btn = QPushButton("瀏覽")
        backup_layout.addWidget(self.browse_backup_btn, 0, 2)
        
        backup_layout.addWidget(QLabel("自動備份頻率:"), 1, 0)
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["每日", "每週", "每月"])
        backup_layout.addWidget(self.backup_frequency_combo, 1, 1)
        
        layout.addWidget(backup_group)
        
        # 備份操作組
        operation_group = QGroupBox("備份操作")
        operation_layout = QVBoxLayout(operation_group)
        
        button_layout = QHBoxLayout()
        
        self.backup_now_btn = QPushButton("立即備份")
        self.backup_now_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.backup_now_btn)
        
        self.restore_btn = QPushButton("恢復數據")
        self.restore_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        button_layout.addWidget(self.restore_btn)
        
        button_layout.addStretch()
        operation_layout.addLayout(button_layout)
        
        # 進度條
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        operation_layout.addWidget(self.backup_progress)
        
        layout.addWidget(operation_group)
        layout.addStretch()
        
        self.tab_widget.addTab(backup_widget, "數據備份")
    
    def _connect_signals(self):
        """連接信號和槽"""
        self.save_btn.clicked.connect(self._save_settings)
        self.reset_btn.clicked.connect(self._reset_settings)
        self.change_password_btn.clicked.connect(self._change_password)
        
        if self.is_admin:
            self.browse_db_btn.clicked.connect(self._browse_database)
            self.add_user_btn.clicked.connect(self._add_user)
            self.edit_user_btn.clicked.connect(self._edit_user)
            self.delete_user_btn.clicked.connect(self._delete_user)
            self.users_table.itemSelectionChanged.connect(self._on_user_selected)
            self.browse_backup_btn.clicked.connect(self._browse_backup_path)
            self.backup_now_btn.clicked.connect(self._backup_now)
            self.restore_btn.clicked.connect(self._restore_data)
    
    def _load_settings(self):
        """加載設置"""
        # 這裡應該從配置文件或數據庫加載設置
        if self.is_admin:
            self._load_users()
    
    def _load_users(self):
        """加載用戶列表"""
        # 這裡應該從數據庫加載用戶列表
        # 暫時使用示例數據
        sample_users = [
            {'username': 'admin', 'full_name': '管理員', 'email': '<EMAIL>', 'role': 'admin', 'status': '啟用'},
            {'username': 'user1', 'full_name': '用戶1', 'email': '<EMAIL>', 'role': 'user', 'status': '啟用'},
        ]
        
        self.users_table.setRowCount(len(sample_users))
        for row, user in enumerate(sample_users):
            self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
            self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
            self.users_table.setItem(row, 2, QTableWidgetItem(user['email']))
            self.users_table.setItem(row, 3, QTableWidgetItem(user['role']))
            self.users_table.setItem(row, 4, QTableWidgetItem(user['status']))
    
    def _save_settings(self):
        """保存設置"""
        # 這裡應該實現保存設置的邏輯
        QMessageBox.information(self, "成功", "設置已保存")
        self.settings_changed.emit({})
    
    def _reset_settings(self):
        """重置設置"""
        reply = QMessageBox.question(
            self, "確認重置", "確定要重置所有設置嗎？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._load_settings()
    
    def _change_password(self):
        """修改密碼"""
        current_password = self.current_password_edit.text()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        if not current_password:
            QMessageBox.warning(self, "警告", "請輸入當前密碼")
            return
        
        if not new_password:
            QMessageBox.warning(self, "警告", "請輸入新密碼")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, "警告", "新密碼和確認密碼不匹配")
            return
        
        # 這裡應該實現密碼修改邏輯
        QMessageBox.information(self, "成功", "密碼已修改")
        
        # 清空密碼字段
        self.current_password_edit.clear()
        self.new_password_edit.clear()
        self.confirm_password_edit.clear()
    
    def _browse_database(self):
        """瀏覽數據庫文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "選擇數據庫文件", "", "SQLite 文件 (*.sqlite *.db)"
        )
        if file_path:
            self.db_path_edit.setText(file_path)
    
    def _add_user(self):
        """添加用戶"""
        QMessageBox.information(self, "提示", "添加用戶功能待實現")
    
    def _edit_user(self):
        """編輯用戶"""
        QMessageBox.information(self, "提示", "編輯用戶功能待實現")
    
    def _delete_user(self):
        """刪除用戶"""
        QMessageBox.information(self, "提示", "刪除用戶功能待實現")
    
    def _on_user_selected(self):
        """用戶選中事件"""
        has_selection = len(self.users_table.selectedItems()) > 0
        self.edit_user_btn.setEnabled(has_selection)
        self.delete_user_btn.setEnabled(has_selection)
    
    def _browse_backup_path(self):
        """瀏覽備份路徑"""
        dir_path = QFileDialog.getExistingDirectory(self, "選擇備份目錄")
        if dir_path:
            self.backup_path_edit.setText(dir_path)
    
    def _backup_now(self):
        """立即備份"""
        self.backup_progress.setVisible(True)
        self.backup_progress.setValue(0)
        
        # 模擬備份過程
        self.backup_timer = QTimer()
        self.backup_timer.timeout.connect(self._update_backup_progress)
        self.backup_progress_value = 0
        self.backup_timer.start(100)
    
    def _update_backup_progress(self):
        """更新備份進度"""
        self.backup_progress_value += 10
        self.backup_progress.setValue(self.backup_progress_value)
        
        if self.backup_progress_value >= 100:
            self.backup_timer.stop()
            self.backup_progress.setVisible(False)
            QMessageBox.information(self, "成功", "備份完成")
    
    def _restore_data(self):
        """恢復數據"""
        reply = QMessageBox.question(
            self, "確認恢復", "確定要恢復數據嗎？這將覆蓋當前數據。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "提示", "數據恢復功能待實現")


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1)
