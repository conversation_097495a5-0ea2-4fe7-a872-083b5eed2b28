#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 查詢出貨頁面

此模塊實現了應用程序的查詢出貨頁面，允許用戶根據各種條件查詢出貨記錄。
"""

import os
import sys
import logging
from typing import Dict, Any
from datetime import datetime, timedelta

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QFrame, 
    QGridLayout, QDateEdit, QComboBox, QHeaderView,
    QSpacerItem, QSizePolicy, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QIcon, QFont

from src.services.shipment_service import ShipmentService


class QueryShipmentWidget(QWidget):
    """
    查詢出貨頁面類，實現出貨記錄的查詢和顯示
    """
    
    def __init__(self, user_info: Dict[str, Any], env_info: Dict[str, Any]):
        """
        初始化查詢出貨頁面
        
        Args:
            user_info: 當前登入用戶的信息
            env_info: 環境信息，包括數據目錄、調試模式等
        """
        super().__init__()
        
        self.user_info = user_info
        self.env_info = env_info
        self.shipment_service = ShipmentService()
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """設置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 查詢條件區域
        query_frame = QFrame()
        query_frame.setFrameShape(QFrame.StyledPanel)
        query_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 5px;
                border: 1px solid #dcdcdc;
            }
        """)
        
        query_layout = QVBoxLayout(query_frame)
        query_layout.setContentsMargins(20, 15, 20, 15)
        
        # 查詢標題
        query_title = QLabel("查詢條件")
        query_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        # 分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #dcdcdc;")
        
        # 查詢表單
        form_layout = QGridLayout()
        form_layout.setSpacing(15)
        
        # 訂單編號
        order_number_label = QLabel("訂單編號:")
        self.order_number_edit = QLineEdit()
        self.order_number_edit.setPlaceholderText("請輸入訂單編號")
        
        # 追蹤編號
        tracking_number_label = QLabel("追蹤編號:")
        self.tracking_number_edit = QLineEdit()
        self.tracking_number_edit.setPlaceholderText("請輸入追蹤編號")
        
        # 客戶名稱
        customer_name_label = QLabel("客戶名稱:")
        self.customer_name_edit = QLineEdit()
        self.customer_name_edit.setPlaceholderText("請輸入客戶名稱")
        
        # 出貨日期範圍
        date_range_label = QLabel("出貨日期範圍:")
        date_layout = QHBoxLayout()
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDisplayFormat("yyyy-MM-dd")
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        
        date_separator = QLabel("至")
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDisplayFormat("yyyy-MM-dd")
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        
        date_layout.addWidget(self.start_date_edit)
        date_layout.addWidget(date_separator)
        date_layout.addWidget(self.end_date_edit)
        date_layout.addStretch()
        
        # 出貨狀態
        status_label = QLabel("出貨狀態:")
        self.status_combo = QComboBox()
        self.status_combo.addItem("全部", -1)
        self.status_combo.addItem("包裝中", 1)
        self.status_combo.addItem("已出貨", 2)
        self.status_combo.addItem("已送達", 3)
        
        # 操作員
        operator_label = QLabel("操作員:")
        self.operator_combo = QComboBox()
        self.operator_combo.addItem("全部", -1)
        # 後續從後端獲取操作員列表並填充
        
        # 添加到表單佈局
        form_layout.addWidget(order_number_label, 0, 0)
        form_layout.addWidget(self.order_number_edit, 0, 1)
        form_layout.addWidget(tracking_number_label, 0, 2)
        form_layout.addWidget(self.tracking_number_edit, 0, 3)
        form_layout.addWidget(customer_name_label, 1, 0)
        form_layout.addWidget(self.customer_name_edit, 1, 1)
        form_layout.addWidget(date_range_label, 1, 2)
        form_layout.addLayout(date_layout, 1, 3)
        form_layout.addWidget(status_label, 2, 0)
        form_layout.addWidget(self.status_combo, 2, 1)
        form_layout.addWidget(operator_label, 2, 2)
        form_layout.addWidget(self.operator_combo, 2, 3)
        
        # 查詢按鈕
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.search_button = QPushButton("查詢")
        self.search_button.setFixedSize(120, 36)
        self.search_button.setIcon(QIcon(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "search.png"
        )))
        self.search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.setFixedSize(120, 36)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #ecf0f1;
                color: #333;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #bdc3c7;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(self.reset_button)
        button_layout.addWidget(self.search_button)
        
        # 組合查詢區域
        query_layout.addWidget(query_title)
        query_layout.addWidget(separator)
        query_layout.addLayout(form_layout)
        query_layout.addLayout(button_layout)
        
        # 查詢結果區域
        result_frame = QFrame()
        result_frame.setFrameShape(QFrame.StyledPanel)
        result_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 5px;
                border: 1px solid #dcdcdc;
            }
        """)
        
        result_layout = QVBoxLayout(result_frame)
        result_layout.setContentsMargins(20, 15, 20, 15)
        
        # 結果標題
        result_title = QLabel("查詢結果")
        result_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        # 分隔線
        result_separator = QFrame()
        result_separator.setFrameShape(QFrame.HLine)
        result_separator.setFrameShadow(QFrame.Sunken)
        result_separator.setStyleSheet("background-color: #dcdcdc;")
        
        # 結果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(8)
        self.result_table.setHorizontalHeaderLabels([
            "訂單編號", "客戶名稱", "追蹤編號", "出貨日期", 
            "操作員", "出貨狀態", "商品數量", "操作"
        ])
        
        # 設置表格樣式
        self.result_table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #dcdcdc;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                color: #333;
                padding: 5px;
                border: 1px solid #dcdcdc;
            }
        """)
        
        # 設置表格列寬
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        header.setSectionResizeMode(5, QHeaderView.Stretch)
        header.setSectionResizeMode(6, QHeaderView.Stretch)
        header.setSectionResizeMode(7, QHeaderView.Stretch)
        
        # 組合結果區域
        result_layout.addWidget(result_title)
        result_layout.addWidget(result_separator)
        result_layout.addWidget(self.result_table)
        
        # 組合主佈局
        main_layout.addWidget(query_frame, 1)
        main_layout.addWidget(result_frame, 3)
    
    def _connect_signals(self):
        """連接信號和槽"""
        self.search_button.clicked.connect(self._handle_search)
        self.reset_button.clicked.connect(self._handle_reset)
        self.result_table.cellClicked.connect(self._handle_cell_clicked)
    
    def _handle_search(self):
        """處理查詢操作"""
        # 獲取查詢條件
        order_number = self.order_number_edit.text().strip()
        tracking_number = self.tracking_number_edit.text().strip()
        customer_name = self.customer_name_edit.text().strip()
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        status = self.status_combo.currentData()
        operator_id = self.operator_combo.currentData()
        
        try:
            # 調用服務查詢出貨記錄
            shipments = self.shipment_service.search_shipments(
                order_number=order_number,
                tracking_number=tracking_number,
                customer_name=customer_name,
                start_date=start_date,
                end_date=end_date,
                status=status,
                operator_id=operator_id
            )
            
            # 更新表格顯示
            self._update_table(shipments)
            
        except Exception as e:
            logging.error(f"查詢出貨記錄錯誤: {str(e)}")
            QMessageBox.critical(self, "查詢錯誤", f"查詢出貨記錄失敗: {str(e)}")
    
    def _handle_reset(self):
        """處理重置操作"""
        self.order_number_edit.clear()
        self.tracking_number_edit.clear()
        self.customer_name_edit.clear()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.end_date_edit.setDate(QDate.currentDate())
        self.status_combo.setCurrentIndex(0)
        self.operator_combo.setCurrentIndex(0)
        
        # 清空表格
        self.result_table.setRowCount(0)
    
    def _update_table(self, shipments):
        """
        更新表格顯示
        
        Args:
            shipments: 出貨記錄列表
        """
        # 清空表格
        self.result_table.setRowCount(0)
        
        if not shipments:
            QMessageBox.information(self, "查詢結果", "沒有找到符合條件的出貨記錄")
            return
        
        # 填充表格
        for row, shipment in enumerate(shipments):
            self.result_table.insertRow(row)
            
            # 訂單編號
            order_number_item = QTableWidgetItem(shipment.get('order_number', ''))
            order_number_item.setFlags(order_number_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 0, order_number_item)
            
            # 客戶名稱
            customer_name_item = QTableWidgetItem(shipment.get('customer_name', ''))
            customer_name_item.setFlags(customer_name_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 1, customer_name_item)
            
            # 追蹤編號
            tracking_number_item = QTableWidgetItem(shipment.get('tracking_number', ''))
            tracking_number_item.setFlags(tracking_number_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 2, tracking_number_item)
            
            # 出貨日期
            shipped_date = shipment.get('shipped_date', '')
            shipped_date_item = QTableWidgetItem(shipped_date)
            shipped_date_item.setFlags(shipped_date_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 3, shipped_date_item)
            
            # 操作員
            operator_name_item = QTableWidgetItem(shipment.get('operator_name', ''))
            operator_name_item.setFlags(operator_name_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 4, operator_name_item)
            
            # 出貨狀態
            status_map = {1: '包裝中', 2: '已出貨', 3: '已送達'}
            status_text = status_map.get(shipment.get('status', 0), '未知')
            status_item = QTableWidgetItem(status_text)
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 5, status_item)
            
            # 商品數量
            item_count_item = QTableWidgetItem(str(shipment.get('item_count', 0)))
            item_count_item.setFlags(item_count_item.flags() & ~Qt.ItemIsEditable)
            self.result_table.setItem(row, 6, item_count_item)
            
            # 操作按鈕
            view_button = QPushButton("查看詳情")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 3px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            # 存儲出貨ID
            view_button.setProperty("shipment_id", shipment.get('shipment_id'))
            
            # 設置按鈕到表格
            self.result_table.setCellWidget(row, 7, view_button)
            
            # 連接按鈕信號
            view_button.clicked.connect(lambda checked, s_id=shipment.get('shipment_id'): 
                                        self._view_shipment_details(s_id))
    
    def _handle_cell_clicked(self, row, column):
        """
        處理表格單元格點擊事件
        
        Args:
            row: 行索引
            column: 列索引
        """
        if column == 7:  # 操作列
            # 獲取按鈕的出貨ID
            button = self.result_table.cellWidget(row, column)
            if button:
                shipment_id = button.property("shipment_id")
                self._view_shipment_details(shipment_id)
    
    def _view_shipment_details(self, shipment_id):
        """
        查看出貨詳情
        
        Args:
            shipment_id: 出貨ID
        """
        if not shipment_id:
            return
        
        try:
            # 調用服務獲取出貨詳情
            shipment = self.shipment_service.get_shipment_details(shipment_id)
            
            if shipment:
                # TODO: 顯示出貨詳情對話框
                QMessageBox.information(
                    self, 
                    "出貨詳情", 
                    f"訂單編號: {shipment.get('order_number')}\n"
                    f"客戶名稱: {shipment.get('customer_name')}\n"
                    f"出貨日期: {shipment.get('shipped_date')}\n"
                    f"(此處將顯示完整詳情對話框)"
                )
            else:
                QMessageBox.warning(self, "查詢詳情失敗", "無法獲取出貨詳情，記錄可能已被刪除")
                
        except Exception as e:
            logging.error(f"獲取出貨詳情錯誤: {str(e)}")
            QMessageBox.critical(self, "查詢詳情錯誤", f"獲取出貨詳情失敗: {str(e)}")


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1) 