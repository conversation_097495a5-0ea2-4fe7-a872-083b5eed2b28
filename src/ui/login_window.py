#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 登入窗口

此模塊實現了應用程序的登入界面，包括用戶認證和訂閱驗證功能。
"""

import os
import sys
import logging
from typing import Dict, Any

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QMessageBox, QCheckBox, QFrame, QGridLayout,
    QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QIcon, QFont

from src.services.auth_service import AuthService
from src.services.subscription_service import SubscriptionService
from src.utils.hardware import get_device_info
from src.utils.validation import validate_input


class LoginWindow(QWidget):
    """
    登入窗口類，處理用戶登入和訂閱驗證
    """
    
    # 自定義信號，在登入成功後發出
    login_successful = Signal(dict)
    
    def __init__(self):
        """初始化登入窗口"""
        super().__init__()
        
        self.auth_service = AuthService()
        self.subscription_service = SubscriptionService()
        
        self.setWindowTitle("出貨記錄工具 - 登入")
        self.setMinimumSize(400, 500)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        # 設置圖標
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "app_icon.png"
        )
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """設置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # 標題區域
        title_layout = QHBoxLayout()
        logo_label = QLabel()
        logo_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "logo.png"
        )
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setFixedSize(64, 64)
        
        title_label = QLabel("出貨記錄工具")
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        title_layout.addWidget(logo_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # 分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        # 登入表單
        form_layout = QGridLayout()
        form_layout.setSpacing(10)
        
        username_label = QLabel("用戶名:")
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("請輸入用戶名")
        
        password_label = QLabel("密碼:")
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("請輸入密碼")
        self.password_edit.setEchoMode(QLineEdit.Password)
        
        license_key_label = QLabel("許可證密鑰:")
        self.license_key_edit = QLineEdit()
        self.license_key_edit.setPlaceholderText("請輸入許可證密鑰")
        
        self.remember_checkbox = QCheckBox("記住用戶名")
        
        # 添加到表單佈局
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_edit, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_edit, 1, 1)
        form_layout.addWidget(license_key_label, 2, 0)
        form_layout.addWidget(self.license_key_edit, 2, 1)
        form_layout.addWidget(self.remember_checkbox, 3, 0, 1, 2)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("登入")
        self.login_button.setFixedHeight(40)
        self.login_button.setDefault(True)
        
        self.exit_button = QPushButton("退出")
        self.exit_button.setFixedHeight(40)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.exit_button)
        
        # 底部信息
        version_label = QLabel("版本: 1.0.0")
        copyright_label = QLabel("© 2023 Example Company. 保留所有權利。")
        copyright_label.setAlignment(Qt.AlignCenter)
        
        # 將所有佈局添加到主佈局
        main_layout.addLayout(title_layout)
        main_layout.addWidget(separator)
        main_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        main_layout.addLayout(form_layout)
        main_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        main_layout.addLayout(button_layout)
        main_layout.addWidget(version_label)
        main_layout.addWidget(copyright_label)
        
        # 加載保存的用戶名
        self._load_saved_username()
    
    def _connect_signals(self):
        """連接信號和槽"""
        self.login_button.clicked.connect(self._handle_login)
        self.exit_button.clicked.connect(self.close)
        self.password_edit.returnPressed.connect(self._handle_login)
        self.license_key_edit.returnPressed.connect(self._handle_login)
    
    def _load_saved_username(self):
        """加載保存的用戶名"""
        # 從配置文件中加載保存的用戶名
        saved_username = self.auth_service.get_saved_username()
        if saved_username:
            self.username_edit.setText(saved_username)
            self.remember_checkbox.setChecked(True)
            self.password_edit.setFocus()
        else:
            self.username_edit.setFocus()
    
    def _save_username(self, username):
        """保存用戶名"""
        if self.remember_checkbox.isChecked():
            self.auth_service.save_username(username)
        else:
            self.auth_service.clear_saved_username()
    
    def _handle_login(self):
        """處理登入操作"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        license_key = self.license_key_edit.text().strip()
        
        # 驗證輸入
        if not validate_input(username, min_length=3, max_length=20, 
                              pattern=r'^[a-zA-Z0-9_]+$', error_msg="用戶名格式不正確"):
            QMessageBox.warning(self, "輸入錯誤", "用戶名必須是3-20個字符，只能包含字母、數字和下劃線")
            self.username_edit.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "輸入錯誤", "請輸入密碼")
            self.password_edit.setFocus()
            return
        
        if not license_key:
            QMessageBox.warning(self, "輸入錯誤", "請輸入許可證密鑰")
            self.license_key_edit.setFocus()
            return
        
        # 獲取設備信息
        device_info = get_device_info()
        
        # 驗證許可證
        try:
            subscription_valid = self.subscription_service.validate_subscription(
                license_key, device_info
            )
            if not subscription_valid:
                QMessageBox.critical(self, "訂閱錯誤", "許可證無效或已過期")
                return
        except Exception as e:
            logging.error(f"訂閱驗證錯誤: {str(e)}")
            QMessageBox.critical(self, "訂閱錯誤", f"訂閱驗證失敗: {str(e)}")
            return
        
        # 驗證用戶身份
        try:
            user_info = self.auth_service.login(username, password, device_info)
            if not user_info:
                QMessageBox.warning(self, "登入失敗", "用戶名或密碼錯誤")
                return
        except Exception as e:
            logging.error(f"登入錯誤: {str(e)}")
            QMessageBox.critical(self, "登入錯誤", f"登入失敗: {str(e)}")
            return
        
        # 保存用戶名（如果勾選了記住用戶名）
        self._save_username(username)
        
        # 發出登入成功信號
        self.login_successful.emit(user_info)
        
    def closeEvent(self, event):
        """處理窗口關閉事件"""
        # 可以在這裡添加關閉確認等邏輯
        event.accept()


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1) 