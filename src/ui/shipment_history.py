#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 出貨記錄界面

此模塊實現出貨記錄查看的用戶界面，包括記錄列表、詳細信息查看、導出等功能。
"""

import os
import sys
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QMessageBox, QTextEdit, QDialog, QDialogButtonBox,
    QSplitter, QFrame, QCheckBox, QSpinBox
)
from PySide6.QtCore import Qt, Signal, QDate, QTimer
from PySide6.QtGui import QFont, QIcon


class ShipmentHistoryWidget(QWidget):
    """
    出貨記錄界面類
    """
    
    # 自定義信號
    record_selected = Signal(dict)  # 記錄選中信號
    
    def __init__(self, user_info: Dict[str, Any], env_info: Dict[str, Any]):
        """
        初始化出貨記錄界面
        
        Args:
            user_info: 當前登入用戶的信息
            env_info: 環境信息，包括數據目錄、調試模式等
        """
        super().__init__()
        
        self.user_info = user_info
        self.env_info = env_info
        self.current_records = []
        
        self._setup_ui()
        self._connect_signals()
        self._load_initial_data()
    
    def _setup_ui(self):
        """設置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 標題
        title_label = QLabel("出貨記錄")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 搜索和篩選區域
        filter_group = QGroupBox("搜索和篩選")
        filter_layout = QGridLayout(filter_group)
        
        # 日期範圍
        filter_layout.addWidget(QLabel("開始日期:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.start_date_edit, 0, 1)
        
        filter_layout.addWidget(QLabel("結束日期:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.end_date_edit, 0, 3)
        
        # 客戶名稱搜索
        filter_layout.addWidget(QLabel("客戶名稱:"), 1, 0)
        self.customer_search_edit = QLineEdit()
        self.customer_search_edit.setPlaceholderText("請輸入客戶名稱")
        filter_layout.addWidget(self.customer_search_edit, 1, 1)
        
        # 出貨單號搜索
        filter_layout.addWidget(QLabel("出貨單號:"), 1, 2)
        self.shipment_number_search_edit = QLineEdit()
        self.shipment_number_search_edit.setPlaceholderText("請輸入出貨單號")
        filter_layout.addWidget(self.shipment_number_search_edit, 1, 3)
        
        # 搜索按鈕
        button_layout = QHBoxLayout()
        self.search_btn = QPushButton("搜索")
        self.search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.search_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        button_layout.addWidget(self.reset_btn)
        
        self.export_btn = QPushButton("導出")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        filter_layout.addLayout(button_layout, 2, 0, 1, 4)
        
        main_layout.addWidget(filter_group)
        
        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左側：記錄列表
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 記錄統計
        stats_label = QLabel("共找到 0 條記錄")
        stats_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 10px;
            }
        """)
        self.stats_label = stats_label
        left_layout.addWidget(stats_label)
        
        # 記錄表格
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(6)
        self.records_table.setHorizontalHeaderLabels([
            "出貨單號", "客戶名稱", "出貨日期", "產品數量", "總金額", "狀態"
        ])
        
        # 設置表格樣式
        header = self.records_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.records_table.setSelectionMode(QTableWidget.SingleSelection)
        
        left_layout.addWidget(self.records_table)
        
        # 右側：詳細信息
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        detail_title = QLabel("出貨詳細信息")
        detail_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        right_layout.addWidget(detail_title)
        
        # 詳細信息顯示區域
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setPlaceholderText("請選擇一條記錄查看詳細信息")
        right_layout.addWidget(self.detail_text)
        
        # 操作按鈕
        detail_button_layout = QHBoxLayout()
        
        self.view_products_btn = QPushButton("查看產品明細")
        self.view_products_btn.setEnabled(False)
        self.view_products_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        detail_button_layout.addWidget(self.view_products_btn)
        
        self.print_btn = QPushButton("打印")
        self.print_btn.setEnabled(False)
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        detail_button_layout.addWidget(self.print_btn)
        
        detail_button_layout.addStretch()
        right_layout.addLayout(detail_button_layout)
        
        # 添加到分割器
        splitter.addWidget(left_frame)
        splitter.addWidget(right_frame)
        splitter.setSizes([600, 400])
        
        main_layout.addWidget(splitter)
    
    def _connect_signals(self):
        """連接信號和槽"""
        self.search_btn.clicked.connect(self._search_records)
        self.reset_btn.clicked.connect(self._reset_filters)
        self.export_btn.clicked.connect(self._export_records)
        self.records_table.itemSelectionChanged.connect(self._on_record_selected)
        self.view_products_btn.clicked.connect(self._view_product_details)
        self.print_btn.clicked.connect(self._print_record)
    
    def _load_initial_data(self):
        """加載初始數據"""
        self._search_records()
    
    def _search_records(self):
        """搜索記錄"""
        # 這裡應該從數據庫搜索記錄
        # 暫時使用示例數據
        sample_records = [
            {
                'shipment_number': 'SH20231201001',
                'customer': '客戶A',
                'date': '2023-12-01',
                'product_count': 5,
                'total_amount': 5000.00,
                'status': '已完成',
                'notes': '正常出貨',
                'products': [
                    {'name': '產品A', 'quantity': 2, 'price': 1000.00},
                    {'name': '產品B', 'quantity': 3, 'price': 1000.00}
                ]
            },
            {
                'shipment_number': 'SH20231202001',
                'customer': '客戶B',
                'date': '2023-12-02',
                'product_count': 3,
                'total_amount': 3000.00,
                'status': '已完成',
                'notes': '加急出貨',
                'products': [
                    {'name': '產品C', 'quantity': 3, 'price': 1000.00}
                ]
            }
        ]
        
        self.current_records = sample_records
        self._update_table()
    
    def _update_table(self):
        """更新表格"""
        self.records_table.setRowCount(len(self.current_records))
        
        for row, record in enumerate(self.current_records):
            self.records_table.setItem(row, 0, QTableWidgetItem(record['shipment_number']))
            self.records_table.setItem(row, 1, QTableWidgetItem(record['customer']))
            self.records_table.setItem(row, 2, QTableWidgetItem(record['date']))
            self.records_table.setItem(row, 3, QTableWidgetItem(str(record['product_count'])))
            self.records_table.setItem(row, 4, QTableWidgetItem(f"${record['total_amount']:.2f}"))
            self.records_table.setItem(row, 5, QTableWidgetItem(record['status']))
        
        # 更新統計信息
        self.stats_label.setText(f"共找到 {len(self.current_records)} 條記錄")
    
    def _reset_filters(self):
        """重置篩選條件"""
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.end_date_edit.setDate(QDate.currentDate())
        self.customer_search_edit.clear()
        self.shipment_number_search_edit.clear()
        self._search_records()
    
    def _export_records(self):
        """導出記錄"""
        if not self.current_records:
            QMessageBox.information(self, "提示", "沒有可導出的記錄")
            return
        
        # 這裡應該實現導出功能
        QMessageBox.information(self, "提示", f"已導出 {len(self.current_records)} 條記錄")
    
    def _on_record_selected(self):
        """記錄選中事件"""
        current_row = self.records_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_records):
            record = self.current_records[current_row]
            self._show_record_details(record)
            self.view_products_btn.setEnabled(True)
            self.print_btn.setEnabled(True)
            self.record_selected.emit(record)
        else:
            self.detail_text.clear()
            self.view_products_btn.setEnabled(False)
            self.print_btn.setEnabled(False)
    
    def _show_record_details(self, record: Dict[str, Any]):
        """顯示記錄詳細信息"""
        details = f"""
出貨單號: {record['shipment_number']}
客戶名稱: {record['customer']}
出貨日期: {record['date']}
產品數量: {record['product_count']}
總金額: ${record['total_amount']:.2f}
狀態: {record['status']}
備註: {record.get('notes', '無')}

產品明細:
"""
        
        for i, product in enumerate(record.get('products', []), 1):
            details += f"{i}. {product['name']} - 數量: {product['quantity']}, 單價: ${product['price']:.2f}\n"
        
        self.detail_text.setPlainText(details)
    
    def _view_product_details(self):
        """查看產品明細"""
        current_row = self.records_table.currentRow()
        if current_row >= 0:
            record = self.current_records[current_row]
            # 這裡可以打開一個詳細的產品明細對話框
            QMessageBox.information(self, "產品明細", f"出貨單號: {record['shipment_number']}\n產品明細功能待實現")
    
    def _print_record(self):
        """打印記錄"""
        current_row = self.records_table.currentRow()
        if current_row >= 0:
            record = self.current_records[current_row]
            # 這裡應該實現打印功能
            QMessageBox.information(self, "打印", f"正在打印出貨單: {record['shipment_number']}")


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1)
