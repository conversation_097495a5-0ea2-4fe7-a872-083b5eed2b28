#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 主窗口

此模塊實現了應用程序的主窗口，包括左側導航欄和右側內容區域。
"""

import os
import sys
import logging
from typing import Dict, Any
from pathlib import Path

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QStackedWidget, QSizePolicy,
    QToolButton, QSpacerItem, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QFont, QPixmap

from src.ui.query_shipment import QueryShipmentWidget
from src.ui.process_shipment import ProcessShipmentWidget
from src.ui.shipment_history import ShipmentHistoryWidget
from src.ui.settings import SettingsWidget
from src.services.auth_service import AuthService


class MainWindow(QMainWindow):
    """
    主窗口類，實現應用的主界面
    """
    
    # 自定義信號，在用戶登出時發出
    logout_requested = Signal()
    
    def __init__(self, user_info: Dict[str, Any], env_info: Dict[str, Any]):
        """
        初始化主窗口
        
        Args:
            user_info: 當前登入用戶的信息
            env_info: 環境信息，包括數據目錄、調試模式等
        """
        super().__init__()
        
        self.user_info = user_info
        self.env_info = env_info
        self.auth_service = AuthService()
        
        self.setWindowTitle("出貨記錄工具")
        self.setMinimumSize(1000, 600)
        
        # 設置圖標
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "app_icon.png"
        )
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        self._setup_ui()
        self._connect_signals()
        
        # 默認顯示歡迎頁面
        self.nav_buttons[0].click()
    
    def _setup_ui(self):
        """設置UI界面"""
        # 創建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左側導航欄
        nav_frame = QFrame()
        nav_frame.setFixedWidth(200)
        nav_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                color: white;
            }
        """)
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(0)
        
        # 頂部標誌和標題
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #243342;
                border-bottom: 1px solid #34495e;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 10, 10, 10)
        
        logo_label = QLabel()
        logo_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "logo.png"
        )
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setFixedSize(48, 48)
        
        app_name_label = QLabel("出貨記錄工具")
        app_name_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(app_name_label)
        header_layout.addStretch()
        
        # 用戶信息區域
        user_frame = QFrame()
        user_frame.setFixedHeight(60)
        user_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-bottom: 1px solid #243342;
            }
        """)
        
        user_layout = QVBoxLayout(user_frame)
        user_layout.setContentsMargins(10, 5, 10, 5)
        
        welcome_label = QLabel(f"歡迎, {self.user_info.get('real_name', self.user_info.get('username', '用戶'))}")
        welcome_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
            }
        """)
        
        role_text = "管理員" if self.user_info.get('role') == 'admin' else "操作員"
        role_label = QLabel(f"角色: {role_text}")
        role_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
            }
        """)
        
        user_layout.addWidget(welcome_label)
        user_layout.addWidget(role_label)
        
        # 導航按鈕
        nav_buttons_frame = QFrame()
        nav_buttons_layout = QVBoxLayout(nav_buttons_frame)
        nav_buttons_layout.setContentsMargins(0, 20, 0, 20)
        nav_buttons_layout.setSpacing(10)
        
        # 導航按鈕樣式
        button_style = """
            QToolButton {
                border: none;
                color: white;
                text-align: left;
                padding: 10px;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #34495e;
            }
            QToolButton:checked {
                background-color: #3498db;
                font-weight: bold;
            }
        """
        
        # 創建導航按鈕
        self.nav_buttons = []
        
        # 查詢出貨按鈕
        query_button = QToolButton()
        query_button.setText("  查詢出貨")
        query_button.setIcon(QIcon(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "search.png"
        )))
        query_button.setIconSize(QSize(24, 24))
        query_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        query_button.setFixedHeight(50)
        query_button.setCheckable(True)
        query_button.setStyleSheet(button_style)
        
        # 進行出貨按鈕
        process_button = QToolButton()
        process_button.setText("  進行出貨")
        process_button.setIcon(QIcon(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "shipping.png"
        )))
        process_button.setIconSize(QSize(24, 24))
        process_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        process_button.setFixedHeight(50)
        process_button.setCheckable(True)
        process_button.setStyleSheet(button_style)
        
        # 出貨記錄按鈕
        history_button = QToolButton()
        history_button.setText("  出貨記錄")
        history_button.setIcon(QIcon(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "history.png"
        )))
        history_button.setIconSize(QSize(24, 24))
        history_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        history_button.setFixedHeight(50)
        history_button.setCheckable(True)
        history_button.setStyleSheet(button_style)
        
        # 系統設置按鈕
        settings_button = QToolButton()
        settings_button.setText("  系統設置")
        settings_button.setIcon(QIcon(os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            "resources", "icons", "settings.png"
        )))
        settings_button.setIconSize(QSize(24, 24))
        settings_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        settings_button.setFixedHeight(50)
        settings_button.setCheckable(True)
        settings_button.setStyleSheet(button_style)
        
        self.nav_buttons.extend([query_button, process_button, history_button, settings_button])
        
        for button in self.nav_buttons:
            nav_buttons_layout.addWidget(button)
        
        nav_buttons_layout.addStretch()
        
        # 登出按鈕
        logout_button = QPushButton("登出")
        logout_button.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: #e74c3c;
                color: white;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
                margin: 0 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.logout_button = logout_button
        
        # 組合左側導航欄
        nav_layout.addWidget(header_frame)
        nav_layout.addWidget(user_frame)
        nav_layout.addWidget(nav_buttons_frame)
        nav_layout.addStretch()
        nav_layout.addWidget(logout_button)
        nav_layout.addSpacing(10)
        
        # 右側內容區域
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # 標題欄
        title_frame = QFrame()
        title_frame.setFixedHeight(60)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-bottom: 1px solid #bdc3c7;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 0, 20, 0)
        
        self.page_title_label = QLabel("歡迎使用出貨記錄工具")
        self.page_title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
            }
        """)
        
        title_layout.addWidget(self.page_title_label)
        title_layout.addStretch()
        
        # 內容堆疊窗口
        self.content_stack = QStackedWidget()
        
        # 添加頁面
        self.query_widget = QueryShipmentWidget(self.user_info, self.env_info)
        self.process_widget = ProcessShipmentWidget(self.user_info, self.env_info)
        self.history_widget = ShipmentHistoryWidget(self.user_info, self.env_info)
        self.settings_widget = SettingsWidget(self.user_info, self.env_info)
        
        self.content_stack.addWidget(self.query_widget)
        self.content_stack.addWidget(self.process_widget)
        self.content_stack.addWidget(self.history_widget)
        self.content_stack.addWidget(self.settings_widget)
        
        # 組合右側內容區域
        content_layout.addWidget(title_frame)
        content_layout.addWidget(self.content_stack)
        
        # 組合主佈局
        main_layout.addWidget(nav_frame)
        main_layout.addWidget(content_frame)
    
    def _connect_signals(self):
        """連接信號和槽"""
        # 導航按鈕切換頁面
        self.nav_buttons[0].clicked.connect(lambda: self._switch_page(0, "查詢出貨"))
        self.nav_buttons[1].clicked.connect(lambda: self._switch_page(1, "進行出貨"))
        self.nav_buttons[2].clicked.connect(lambda: self._switch_page(2, "出貨記錄"))
        self.nav_buttons[3].clicked.connect(lambda: self._switch_page(3, "系統設置"))
        
        # 登出按鈕
        self.logout_button.clicked.connect(self._handle_logout)
    
    def _switch_page(self, index, title):
        """
        切換頁面
        
        Args:
            index: 頁面索引
            title: 頁面標題
        """
        # 更新按鈕狀態
        for i, button in enumerate(self.nav_buttons):
            button.setChecked(i == index)
        
        # 更新頁面標題
        self.page_title_label.setText(title)
        
        # 切換頁面
        self.content_stack.setCurrentIndex(index)
    
    def _handle_logout(self):
        """處理登出操作"""
        reply = QMessageBox.question(
            self, "確認登出", "確定要登出系統嗎？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.auth_service.logout()
                self.logout_requested.emit()
            except Exception as e:
                logging.error(f"登出錯誤: {str(e)}")
                QMessageBox.critical(self, "登出錯誤", f"登出失敗: {str(e)}")
    
    def closeEvent(self, event):
        """處理窗口關閉事件"""
        reply = QMessageBox.question(
            self, "確認退出", "確定要退出應用程序嗎？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.auth_service.logout()
            except:
                pass
            event.accept()
        else:
            event.ignore()


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1) 