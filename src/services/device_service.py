#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 設備服務

此模塊提供設備管理和授權功能。
"""

import sys
import logging
import datetime
from typing import Dict, Any, Optional, List

from src.utils.db_utils import DBConnection
from src.utils.hardware import get_device_info, get_mac_address, check_device_id


class DeviceService:
    """
    設備服務類，提供設備管理和授權功能
    """
    
    def __init__(self):
        """初始化設備服務"""
        self.db_conn = DBConnection()
        self.device_info = get_device_info()
        self.mac_address = get_mac_address()
    
    def register_device(self, user_id: int) -> bool:
        """
        註冊設備
        
        Args:
            user_id: 用戶ID
            
        Returns:
            是否註冊成功
        """
        try:
            # 檢查設備ID是否有效
            device_id = self.mac_address
            if not check_device_id(device_id):
                logging.error("設備ID無效")
                return False
            
            # 檢查設備是否已註冊
            if self.is_device_registered(device_id):
                logging.info("設備已註冊，更新用戶ID")
                return self.update_device_user(device_id, user_id)
            
            # 註冊新設備
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            query = """
            INSERT INTO device_registry (device_id, user_id, registration_date, last_login_date, is_active)
            VALUES (?, ?, ?, ?, 1)
            """
            
            self.db_conn.execute_update(query, (device_id, user_id, current_time, current_time))
            logging.info(f"設備 {device_id} 已成功註冊給用戶 {user_id}")
            return True
        
        except Exception as e:
            logging.error(f"註冊設備時發生錯誤: {str(e)}")
            return False
    
    def is_device_registered(self, device_id: Optional[str] = None) -> bool:
        """
        檢查設備是否已註冊
        
        Args:
            device_id: 設備ID，如果為None則使用當前設備
            
        Returns:
            設備是否已註冊
        """
        try:
            if device_id is None:
                device_id = self.mac_address
            
            query = """
            SELECT id FROM device_registry
            WHERE device_id = ? AND is_active = 1
            """
            
            results = self.db_conn.execute_query(query, (device_id,))
            return bool(results)
        
        except Exception as e:
            logging.error(f"檢查設備註冊狀態時發生錯誤: {str(e)}")
            return False
    
    def get_device_info_db(self, device_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        從數據庫獲取設備信息
        
        Args:
            device_id: 設備ID，如果為None則使用當前設備
            
        Returns:
            設備信息字典，如果沒有則返回None
        """
        try:
            if device_id is None:
                device_id = self.mac_address
            
            query = """
            SELECT id, device_id, user_id, registration_date, last_login_date, is_active
            FROM device_registry
            WHERE device_id = ?
            """
            
            results = self.db_conn.execute_query(query, (device_id,))
            
            if not results:
                return None
            
            device = results[0]
            return {
                'id': device['id'],
                'device_id': device['device_id'],
                'user_id': device['user_id'],
                'registration_date': device['registration_date'],
                'last_login_date': device['last_login_date'],
                'is_active': bool(device['is_active'])
            }
        
        except Exception as e:
            logging.error(f"獲取設備信息時發生錯誤: {str(e)}")
            return None
    
    def update_device_last_login(self, device_id: Optional[str] = None) -> bool:
        """
        更新設備最後登錄時間
        
        Args:
            device_id: 設備ID，如果為None則使用當前設備
            
        Returns:
            是否更新成功
        """
        try:
            if device_id is None:
                device_id = self.mac_address
            
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            query = """
            UPDATE device_registry
            SET last_login_date = ?
            WHERE device_id = ? AND is_active = 1
            """
            
            self.db_conn.execute_update(query, (current_time, device_id))
            return True
        
        except Exception as e:
            logging.error(f"更新設備登錄時間時發生錯誤: {str(e)}")
            return False
    
    def update_device_user(self, device_id: str, user_id: int) -> bool:
        """
        更新設備的用戶
        
        Args:
            device_id: 設備ID
            user_id: 用戶ID
            
        Returns:
            是否更新成功
        """
        try:
            query = """
            UPDATE device_registry
            SET user_id = ?
            WHERE device_id = ? AND is_active = 1
            """
            
            self.db_conn.execute_update(query, (user_id, device_id))
            return True
        
        except Exception as e:
            logging.error(f"更新設備用戶時發生錯誤: {str(e)}")
            return False
    
    def deactivate_device(self, device_id: Optional[str] = None) -> bool:
        """
        停用設備
        
        Args:
            device_id: 設備ID，如果為None則使用當前設備
            
        Returns:
            是否停用成功
        """
        try:
            if device_id is None:
                device_id = self.mac_address
            
            query = """
            UPDATE device_registry
            SET is_active = 0
            WHERE device_id = ?
            """
            
            self.db_conn.execute_update(query, (device_id,))
            return True
        
        except Exception as e:
            logging.error(f"停用設備時發生錯誤: {str(e)}")
            return False
    
    def get_user_devices(self, user_id: int) -> List[Dict[str, Any]]:
        """
        獲取用戶的所有設備
        
        Args:
            user_id: 用戶ID
            
        Returns:
            設備信息列表
        """
        try:
            query = """
            SELECT id, device_id, registration_date, last_login_date, is_active
            FROM device_registry
            WHERE user_id = ?
            ORDER BY last_login_date DESC
            """
            
            results = self.db_conn.execute_query(query, (user_id,))
            devices = []
            
            for device in results:
                devices.append({
                    'id': device['id'],
                    'device_id': device['device_id'],
                    'registration_date': device['registration_date'],
                    'last_login_date': device['last_login_date'],
                    'is_active': bool(device['is_active']),
                    'is_current': device['device_id'] == self.mac_address
                })
            
            return devices
        
        except Exception as e:
            logging.error(f"獲取用戶設備列表時發生錯誤: {str(e)}")
            return []
    
    def __del__(self):
        """析構函數，關閉數據庫連接"""
        if hasattr(self, 'db_conn'):
            self.db_conn.close()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 