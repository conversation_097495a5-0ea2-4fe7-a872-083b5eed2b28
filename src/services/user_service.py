#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 用戶服務

此模塊提供用戶相關功能，包括用戶登錄、註冊、密碼管理等。
"""

import os
import sys
import hashlib
import logging
import datetime
import uuid
from typing import Optional, Dict, List, Any, Tuple

# 確保 utils 模塊可以被導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.db_utils import DBConnection


class UserService:
    """用戶服務類"""
    
    def __init__(self):
        """初始化用戶服務"""
        self.db = DBConnection()
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        用戶登錄
        
        Args:
            username: 用戶名
            password: 密碼
            
        Returns:
            如果登錄成功，返回用戶信息；否則返回None
        """
        try:
            # 哈希密碼
            hashed_password = self._hash_password(password)
            
            # 查詢用戶
            query = """
            SELECT id, username, display_name, email, is_admin, is_active, 
                   last_login, created_at, updated_at
            FROM users
            WHERE username = ? AND password = ? AND is_active = 1
            """
            
            results = self.db.execute_query(query, (username, hashed_password))
            
            if not results:
                return None
                
            # 轉換為字典
            user = dict(results[0])
            
            # 更新最後登錄時間
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            update_query = """
            UPDATE users SET last_login = ?, updated_at = ? WHERE id = ?
            """
            
            self.db.execute_update(update_query, (now, now, user['id']))
            
            # 返回用戶信息（不包含密碼）
            return user
            
        except Exception as e:
            logging.error(f"用戶登錄失敗: {str(e)}")
            return None
    
    def check_subscription(self, user_id: int) -> Dict[str, Any]:
        """
        檢查用戶訂閱狀態
        
        Args:
            user_id: 用戶ID
            
        Returns:
            訂閱信息，包括是否有效、過期日期等
        """
        try:
            # 查詢訂閱信息
            query = """
            SELECT * FROM subscription
            WHERE user_id = ?
            ORDER BY expire_date DESC
            LIMIT 1
            """
            
            results = self.db.execute_query(query, (user_id,))
            
            if not results:
                return {
                    "is_active": False,
                    "message": "未找到訂閱記錄",
                    "expire_date": None,
                    "subscription_type": None
                }
            
            # 轉換為字典
            subscription = dict(results[0])
            
            # 檢查訂閱是否有效
            now = datetime.datetime.now().date()
            expire_date_str = subscription['expire_date']
            expire_date = datetime.datetime.strptime(expire_date_str, '%Y-%m-%d').date()
            
            is_active = expire_date >= now
            
            return {
                "is_active": is_active,
                "message": "訂閱有效" if is_active else "訂閱已過期",
                "expire_date": expire_date_str,
                "subscription_type": subscription['subscription_type'],
                "days_left": (expire_date - now).days if is_active else 0
            }
            
        except Exception as e:
            logging.error(f"檢查訂閱狀態失敗: {str(e)}")
            return {
                "is_active": False,
                "message": f"檢查訂閱發生錯誤: {str(e)}",
                "expire_date": None,
                "subscription_type": None
            }
    
    def register_device(self, user_id: int, mac_address: str, device_name: str) -> bool:
        """
        註冊設備MAC地址
        
        Args:
            user_id: 用戶ID
            mac_address: MAC地址
            device_name: 設備名稱
            
        Returns:
            是否註冊成功
        """
        try:
            # 檢查是否已經註冊
            check_query = """
            SELECT COUNT(*) as count FROM devices
            WHERE mac_address = ?
            """
            
            results = self.db.execute_query(check_query, (mac_address,))
            
            if results and results[0]['count'] > 0:
                # 設備已存在，更新用戶ID
                update_query = """
                UPDATE devices 
                SET user_id = ?, device_name = ?, last_login = ?, updated_at = ?
                WHERE mac_address = ?
                """
                
                now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                self.db.execute_update(update_query, (
                    user_id, device_name, now, now, mac_address
                ))
                
                return True
            else:
                # 新設備註冊
                insert_query = """
                INSERT INTO devices (
                    user_id, mac_address, device_name, 
                    last_login, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?)
                """
                
                now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                self.db.execute_update(insert_query, (
                    user_id, mac_address, device_name, now, now, now
                ))
                
                return True
                
        except Exception as e:
            logging.error(f"註冊設備失敗: {str(e)}")
            return False
    
    def check_device_login(self, user_id: int, mac_address: str) -> bool:
        """
        檢查設備登錄狀態
        
        Args:
            user_id: 用戶ID
            mac_address: 設備MAC地址
            
        Returns:
            設備登錄是否有效
        """
        try:
            # 查詢設備記錄
            query = """
            SELECT * FROM devices
            WHERE user_id = ? AND mac_address = ?
            """
            
            results = self.db.execute_query(query, (user_id, mac_address))
            
            if not results:
                return False
                
            # 更新最後登錄時間
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            update_query = """
            UPDATE devices SET last_login = ?, updated_at = ?
            WHERE user_id = ? AND mac_address = ?
            """
            
            self.db.execute_update(update_query, (now, now, user_id, mac_address))
            
            return True
            
        except Exception as e:
            logging.error(f"檢查設備登錄失敗: {str(e)}")
            return False
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        修改用戶密碼
        
        Args:
            user_id: 用戶ID
            old_password: 舊密碼
            new_password: 新密碼
            
        Returns:
            是否修改成功
        """
        try:
            # 驗證舊密碼
            hashed_old_password = self._hash_password(old_password)
            
            check_query = """
            SELECT COUNT(*) as count FROM users
            WHERE id = ? AND password = ?
            """
            
            results = self.db.execute_query(check_query, (user_id, hashed_old_password))
            
            if not results or results[0]['count'] == 0:
                return False
                
            # 更新密碼
            hashed_new_password = self._hash_password(new_password)
            
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            update_query = """
            UPDATE users 
            SET password = ?, updated_at = ?
            WHERE id = ?
            """
            
            self.db.execute_update(update_query, (hashed_new_password, now, user_id))
            
            return True
            
        except Exception as e:
            logging.error(f"修改密碼失敗: {str(e)}")
            return False
    
    def create_user(
        self, 
        username: str, 
        password: str, 
        display_name: Optional[str] = None,
        email: Optional[str] = None,
        is_admin: bool = False
    ) -> Optional[int]:
        """
        創建新用戶
        
        Args:
            username: 用戶名
            password: 密碼
            display_name: 顯示名稱
            email: 電子郵件
            is_admin: 是否為管理員
            
        Returns:
            如果創建成功，返回新用戶ID；否則返回None
        """
        try:
            # 檢查用戶名是否已存在
            check_query = """
            SELECT COUNT(*) as count FROM users
            WHERE username = ?
            """
            
            results = self.db.execute_query(check_query, (username,))
            
            if results and results[0]['count'] > 0:
                logging.warning(f"用戶名已存在: {username}")
                return None
                
            # 哈希密碼
            hashed_password = self._hash_password(password)
            
            # 創建用戶
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            insert_query = """
            INSERT INTO users (
                username, password, display_name, email, 
                is_admin, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            display_name = display_name or username
            
            cursor = self.db.execute_update_cursor(insert_query, (
                username, hashed_password, display_name, email,
                1 if is_admin else 0, 1, now, now
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            logging.error(f"創建用戶失敗: {str(e)}")
            return None
    
    def get_users(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """
        獲取用戶列表
        
        Args:
            include_inactive: 是否包含停用的用戶
            
        Returns:
            用戶列表
        """
        try:
            query = """
            SELECT id, username, display_name, email, is_admin, is_active, 
                   last_login, created_at, updated_at
            FROM users
            """
            
            if not include_inactive:
                query += " WHERE is_active = 1"
                
            query += " ORDER BY username"
            
            results = self.db.execute_query(query)
            
            users = []
            for row in results:
                users.append(dict(row))
                
            return users
            
        except Exception as e:
            logging.error(f"獲取用戶列表失敗: {str(e)}")
            return []
    
    def update_user_status(self, user_id: int, is_active: bool) -> bool:
        """
        更新用戶狀態
        
        Args:
            user_id: 用戶ID
            is_active: 是否啟用
            
        Returns:
            是否更新成功
        """
        try:
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            update_query = """
            UPDATE users 
            SET is_active = ?, updated_at = ?
            WHERE id = ?
            """
            
            self.db.execute_update(update_query, (1 if is_active else 0, now, user_id))
            
            return True
            
        except Exception as e:
            logging.error(f"更新用戶狀態失敗: {str(e)}")
            return False
    
    def create_subscription(
        self, 
        user_id: int, 
        expire_date: str,
        subscription_type: str,
        notes: Optional[str] = None
    ) -> Optional[int]:
        """
        創建用戶訂閱
        
        Args:
            user_id: 用戶ID
            expire_date: 過期日期 (YYYY-MM-DD)
            subscription_type: 訂閱類型
            notes: 備註
            
        Returns:
            如果創建成功，返回新訂閱ID；否則返回None
        """
        try:
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            insert_query = """
            INSERT INTO subscription (
                user_id, expire_date, subscription_type, 
                notes, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            """
            
            cursor = self.db.execute_update_cursor(insert_query, (
                user_id, expire_date, subscription_type,
                notes, now, now
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            logging.error(f"創建訂閱失敗: {str(e)}")
            return None
    
    def get_settings(self) -> Dict[str, str]:
        """
        獲取系統設置
        
        Returns:
            系統設置字典
        """
        try:
            query = "SELECT * FROM settings"
            
            results = self.db.execute_query(query)
            
            settings = {}
            for row in results:
                settings[row['key']] = row['value']
                
            return settings
            
        except Exception as e:
            logging.error(f"獲取系統設置失敗: {str(e)}")
            return {}
    
    def update_setting(self, key: str, value: str) -> bool:
        """
        更新系統設置
        
        Args:
            key: 設置鍵
            value: 設置值
            
        Returns:
            是否更新成功
        """
        try:
            # 檢查設置是否已存在
            check_query = "SELECT COUNT(*) as count FROM settings WHERE key = ?"
            
            results = self.db.execute_query(check_query, (key,))
            
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if results and results[0]['count'] > 0:
                # 更新現有設置
                update_query = """
                UPDATE settings 
                SET value = ?, updated_at = ?
                WHERE key = ?
                """
                
                self.db.execute_update(update_query, (value, now, key))
                
            else:
                # 插入新設置
                insert_query = """
                INSERT INTO settings (key, value, created_at, updated_at)
                VALUES (?, ?, ?, ?)
                """
                
                self.db.execute_update(insert_query, (key, value, now, now))
                
            return True
            
        except Exception as e:
            logging.error(f"更新系統設置失敗: {str(e)}")
            return False
    
    def _hash_password(self, password: str) -> str:
        """
        密碼哈希處理
        
        Args:
            password: 原始密碼
            
        Returns:
            哈希後的密碼
        """
        # 此處使用 SHA-256 哈希，實際應用中應考慮使用更安全的方法如 bcrypt
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_reset_token(self, username: str) -> Optional[str]:
        """
        生成密碼重置令牌
        
        Args:
            username: 用戶名
            
        Returns:
            如果成功，返回重置令牌；否則返回None
        """
        try:
            # 查詢用戶
            query = "SELECT id FROM users WHERE username = ? AND is_active = 1"
            
            results = self.db.execute_query(query, (username,))
            
            if not results:
                return None
                
            user_id = results[0]['id']
            
            # 生成令牌
            token = str(uuid.uuid4())
            expire_time = datetime.datetime.now() + datetime.timedelta(hours=24)
            expire_time_str = expire_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 存儲令牌
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 檢查是否已有令牌
            check_query = """
            SELECT COUNT(*) as count FROM password_reset
            WHERE user_id = ?
            """
            
            check_results = self.db.execute_query(check_query, (user_id,))
            
            if check_results and check_results[0]['count'] > 0:
                # 更新現有令牌
                update_query = """
                UPDATE password_reset 
                SET token = ?, expire_time = ?, updated_at = ?
                WHERE user_id = ?
                """
                
                self.db.execute_update(update_query, (token, expire_time_str, now, user_id))
                
            else:
                # 創建新令牌
                insert_query = """
                INSERT INTO password_reset (
                    user_id, token, expire_time, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?)
                """
                
                self.db.execute_update(insert_query, (
                    user_id, token, expire_time_str, now, now
                ))
                
            return token
            
        except Exception as e:
            logging.error(f"生成密碼重置令牌失敗: {str(e)}")
            return None


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 