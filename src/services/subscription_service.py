#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 訂閱服務

此模塊提供訂閱管理和驗證功能。
"""

import sys
import json
import logging
import datetime
import hashlib
from typing import Dict, Any, Optional, List, Tuple

from src.utils.db_utils import DBConnection


class SubscriptionService:
    """
    訂閱服務類，提供訂閱管理和驗證功能
    """
    
    def __init__(self):
        """初始化訂閱服務"""
        self.db_conn = DBConnection()
    
    def validate_subscription(self, license_key: str, device_info: Dict[str, Any]) -> bool:
        """
        驗證許可證密鑰
        
        Args:
            license_key: 許可證密鑰
            device_info: 設備信息
            
        Returns:
            許可證是否有效
        """
        try:
            # 在實際應用中，這裡會有更複雜的許可證驗證邏輯
            # 為了示例，我們使用一個簡單的驗證方法
            
            # 檢查許可證格式
            if not license_key or len(license_key) < 8:
                logging.warning("許可證格式不正確")
                return False
            
            # 檢查是否是演示許可證
            if license_key.upper() == "DEMO-KEY":
                logging.info("使用演示許可證")
                return True
            
            # 在實際應用中，這裡會檢查許可證的有效性，比如：
            # 1. 與遠程服務器驗證
            # 2. 檢查許可證是否過期
            # 3. 檢查設備數量限制
            # 4. 其他業務規則
            
            # 僅作為示例，這裡我們檢查MAC地址與許可證是否匹配
            mac_address = device_info.get('mac_address', '')
            if not mac_address:
                logging.warning("無法獲取MAC地址")
                return False
            
            # 簡單的許可證生成邏輯（僅作示例）
            expected_key = self._generate_license_key(mac_address)
            if license_key.upper() == expected_key.upper():
                logging.info("許可證驗證通過")
                return True
            
            logging.warning("許可證驗證失敗")
            return False
        
        except Exception as e:
            logging.error(f"許可證驗證過程中發生錯誤: {str(e)}")
            return False
    
    def get_subscription_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        獲取用戶訂閱信息
        
        Args:
            user_id: 用戶ID
            
        Returns:
            訂閱信息字典，如果沒有則返回None
        """
        try:
            query = """
            SELECT id, user_id, start_date, expiry_date, is_active
            FROM subscription
            WHERE user_id = ? AND is_active = 1
            ORDER BY expiry_date DESC
            LIMIT 1
            """
            
            results = self.db_conn.execute_query(query, (user_id,))
            
            if not results:
                return None
            
            subscription = results[0]
            
            # 將日期字符串轉換為datetime對象
            start_date = datetime.datetime.strptime(subscription['start_date'], '%Y-%m-%d').date()
            expiry_date = datetime.datetime.strptime(subscription['expiry_date'], '%Y-%m-%d').date()
            
            # 檢查是否過期
            is_expired = expiry_date < datetime.date.today()
            
            return {
                'id': subscription['id'],
                'user_id': subscription['user_id'],
                'start_date': start_date,
                'expiry_date': expiry_date,
                'is_active': bool(subscription['is_active']),
                'is_expired': is_expired,
                'days_left': (expiry_date - datetime.date.today()).days if not is_expired else 0
            }
        
        except Exception as e:
            logging.error(f"獲取訂閱信息失敗: {str(e)}")
            return None
    
    def add_subscription(
        self, 
        user_id: int, 
        days: int, 
        start_date: Optional[datetime.date] = None
    ) -> bool:
        """
        為用戶添加訂閱
        
        Args:
            user_id: 用戶ID
            days: 訂閱天數
            start_date: 開始日期，如果為None則使用今天
            
        Returns:
            是否添加成功
        """
        try:
            if start_date is None:
                start_date = datetime.date.today()
            
            expiry_date = start_date + datetime.timedelta(days=days)
            
            # 格式化日期為字符串
            start_date_str = start_date.strftime('%Y-%m-%d')
            expiry_date_str = expiry_date.strftime('%Y-%m-%d')
            
            # 首先檢查是否有現有訂閱
            query = """
            SELECT id, expiry_date
            FROM subscription
            WHERE user_id = ? AND is_active = 1
            ORDER BY expiry_date DESC
            LIMIT 1
            """
            
            results = self.db_conn.execute_query(query, (user_id,))
            
            if results:
                # 更新現有訂閱
                subscription_id = results[0]['id']
                current_expiry = datetime.datetime.strptime(results[0]['expiry_date'], '%Y-%m-%d').date()
                
                # 如果當前訂閱未過期，則從當前過期日開始添加天數
                if current_expiry > datetime.date.today():
                    new_expiry = current_expiry + datetime.timedelta(days=days)
                else:
                    new_expiry = datetime.date.today() + datetime.timedelta(days=days)
                
                new_expiry_str = new_expiry.strftime('%Y-%m-%d')
                
                update_query = """
                UPDATE subscription
                SET expiry_date = ?
                WHERE id = ?
                """
                
                self.db_conn.execute_update(update_query, (new_expiry_str, subscription_id))
            else:
                # 創建新訂閱
                insert_query = """
                INSERT INTO subscription (user_id, start_date, expiry_date, is_active)
                VALUES (?, ?, ?, 1)
                """
                
                self.db_conn.execute_update(insert_query, (user_id, start_date_str, expiry_date_str))
            
            return True
        
        except Exception as e:
            logging.error(f"添加訂閱失敗: {str(e)}")
            return False
    
    def deactivate_subscription(self, subscription_id: int) -> bool:
        """
        停用訂閱
        
        Args:
            subscription_id: 訂閱ID
            
        Returns:
            是否停用成功
        """
        try:
            update_query = """
            UPDATE subscription
            SET is_active = 0
            WHERE id = ?
            """
            
            self.db_conn.execute_update(update_query, (subscription_id,))
            return True
        
        except Exception as e:
            logging.error(f"停用訂閱失敗: {str(e)}")
            return False
    
    def _generate_license_key(self, mac_address: str) -> str:
        """
        生成許可證密鑰（僅作示例）
        
        Args:
            mac_address: MAC地址
            
        Returns:
            許可證密鑰
        """
        # 這只是一個簡單的示例，實際應用中應該使用更安全的方法
        mac_hash = hashlib.md5(mac_address.encode()).hexdigest()
        return f"LIC-{mac_hash[:8].upper()}"
    
    def __del__(self):
        """析構函數，關閉數據庫連接"""
        if hasattr(self, 'db_conn'):
            self.db_conn.close()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 