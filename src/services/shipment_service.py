#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 出貨服務

此模塊提供出貨記錄相關功能，包括查詢出貨記錄、處理出貨作業等。
"""

import os
import sys
import datetime
import logging
from typing import Optional, Dict, List, Any, Tuple, Union

# 確保 utils 模塊可以被導入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.db_utils import DBConnection


class ShipmentService:
    """出貨服務類"""
    
    def __init__(self):
        """初始化出貨服務"""
        self.db = DBConnection()
    
    def search_shipments(
        self, 
        order_number: Optional[str] = None, 
        tracking_number: Optional[str] = None,
        customer_name: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        status: Optional[int] = None,
        operator_id: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> <PERSON><PERSON>[List[Dict[str, Any]], int]:
        """
        搜索出貨記錄
        
        Args:
            order_number: 訂單編號
            tracking_number: 追蹤號碼
            customer_name: 客戶姓名
            start_date: 開始日期 (YYYY-MM-DD)
            end_date: 結束日期 (YYYY-MM-DD)
            status: 出貨狀態 (1: 包裝中, 2: 已出貨, 3: 已送達)
            operator_id: 操作員ID
            limit: 結果限制數量
            offset: 結果偏移量
            
        Returns:
            出貨記錄列表及總記錄數
        """
        try:
            # 基本查詢語句
            query = """
            SELECT s.*, u.username as operator_name, 
                   (SELECT COUNT(*) FROM shipment_items si WHERE si.shipment_id = s.id) as item_count
            FROM shipments s
            LEFT JOIN users u ON s.operator_id = u.id
            WHERE 1=1
            """
            count_query = "SELECT COUNT(*) as total FROM shipments s WHERE 1=1"
            
            # 查詢參數
            params = []
            where_clauses = []
            
            # 動態添加搜索條件
            if order_number:
                where_clauses.append("s.order_number LIKE ?")
                params.append(f"%{order_number}%")
                
            if tracking_number:
                where_clauses.append("s.tracking_number LIKE ?")
                params.append(f"%{tracking_number}%")
                
            if customer_name:
                where_clauses.append("s.customer_name LIKE ?")
                params.append(f"%{customer_name}%")
                
            if start_date:
                where_clauses.append("s.shipped_date >= ?")
                params.append(f"{start_date} 00:00:00")
                
            if end_date:
                where_clauses.append("s.shipped_date <= ?")
                params.append(f"{end_date} 23:59:59")
                
            if status is not None:
                where_clauses.append("s.status = ?")
                params.append(status)
                
            if operator_id is not None:
                where_clauses.append("s.operator_id = ?")
                params.append(operator_id)
            
            # 添加條件到查詢
            if where_clauses:
                query_conditions = " AND " + " AND ".join(where_clauses)
                query += query_conditions
                count_query += query_conditions
            
            # 添加排序和分頁
            query += " ORDER BY s.created_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            # 執行主查詢
            results = self.db.execute_query(query, tuple(params))
            
            # 執行計數查詢
            count_result = self.db.execute_query(count_query, tuple(params[:-2]))
            total_count = count_result[0]['total'] if count_result else 0
            
            # 轉換結果為字典列表
            shipments = []
            for row in results:
                # 轉換 sqlite3.Row 為字典
                shipment = dict(row)
                shipments.append(shipment)
                
            return shipments, total_count
            
        except Exception as e:
            logging.error(f"搜索出貨記錄失敗: {str(e)}")
            raise Exception(f"搜索出貨記錄失敗: {str(e)}")
    
    def get_shipment_details(self, shipment_id: int) -> Dict[str, Any]:
        """
        獲取出貨詳情
        
        Args:
            shipment_id: 出貨記錄ID
            
        Returns:
            出貨詳情，包括基本信息、物品清單和操作日誌
        """
        try:
            # 獲取基本信息
            query = """
            SELECT s.*, u.username as operator_name
            FROM shipments s
            LEFT JOIN users u ON s.operator_id = u.id
            WHERE s.id = ?
            """
            results = self.db.execute_query(query, (shipment_id,))
            
            if not results:
                raise Exception(f"未找到ID為 {shipment_id} 的出貨記錄")
                
            shipment = dict(results[0])
            
            # 獲取物品清單
            items_query = """
            SELECT si.*, p.name as product_name, p.sku as product_sku
            FROM shipment_items si
            LEFT JOIN products p ON si.product_id = p.id
            WHERE si.shipment_id = ?
            """
            items_results = self.db.execute_query(items_query, (shipment_id,))
            
            items = []
            for row in items_results:
                items.append(dict(row))
            
            # 獲取操作日誌
            logs_query = """
            SELECT sl.*, u.username as operator_name
            FROM shipment_logs sl
            LEFT JOIN users u ON sl.operator_id = u.id
            WHERE sl.shipment_id = ?
            ORDER BY sl.created_at DESC
            """
            logs_results = self.db.execute_query(logs_query, (shipment_id,))
            
            logs = []
            for row in logs_results:
                logs.append(dict(row))
            
            # 組合完整詳情
            shipment_details = {
                "shipment": shipment,
                "items": items,
                "logs": logs
            }
            
            return shipment_details
            
        except Exception as e:
            logging.error(f"獲取出貨詳情失敗: {str(e)}")
            raise Exception(f"獲取出貨詳情失敗: {str(e)}")
    
    def create_shipment(
        self, 
        order_number: str, 
        tracking_number: str,
        customer_name: str,
        customer_address: str,
        customer_phone: str,
        shipping_method: str,
        notes: Optional[str] = None,
        items: List[Dict[str, Any]] = None,
        operator_id: int = 1,
        status: int = 1
    ) -> int:
        """
        創建出貨記錄
        
        Args:
            order_number: 訂單編號
            tracking_number: 追蹤號碼
            customer_name: 客戶姓名
            customer_address: 客戶地址
            customer_phone: 客戶電話
            shipping_method: 發貨方式
            notes: 備註
            items: 物品清單 [{"product_id": 1, "quantity": 2, "unit_price": 100}]
            operator_id: 操作員ID
            status: 出貨狀態 (1: 包裝中, 2: 已出貨, 3: 已送達)
            
        Returns:
            新建的出貨記錄ID
        """
        try:
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            shipped_date = now  # 默認為當前時間
            
            # 創建出貨記錄
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            try:
                # 開始事務
                conn.execute("BEGIN TRANSACTION")
                
                # 插入出貨記錄
                cursor.execute("""
                INSERT INTO shipments (
                    order_number, tracking_number, customer_name, customer_address, 
                    customer_phone, shipped_date, status, shipping_method, 
                    notes, operator_id, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order_number, tracking_number, customer_name, customer_address,
                    customer_phone, shipped_date, status, shipping_method,
                    notes, operator_id, now, now
                ))
                
                # 獲取新插入的出貨ID
                shipment_id = cursor.lastrowid
                
                # 插入物品清單
                if items:
                    for item in items:
                        product_id = item.get('product_id')
                        quantity = item.get('quantity', 1)
                        unit_price = item.get('unit_price', 0)
                        subtotal = quantity * unit_price
                        item_notes = item.get('notes')
                        
                        cursor.execute("""
                        INSERT INTO shipment_items (
                            shipment_id, product_id, quantity, unit_price, 
                            subtotal, notes
                        ) VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            shipment_id, product_id, quantity, unit_price,
                            subtotal, item_notes
                        ))
                
                # 記錄操作日誌
                cursor.execute("""
                INSERT INTO shipment_logs (
                    shipment_id, action, description, operator_id, created_at
                ) VALUES (?, ?, ?, ?, ?)
                """, (
                    shipment_id, "創建出貨", "創建新的出貨記錄", operator_id, now
                ))
                
                # 提交事務
                conn.commit()
                
                return shipment_id
                
            except Exception as e:
                # 回滾事務
                conn.rollback()
                raise Exception(f"創建出貨記錄失敗: {str(e)}")
                
            finally:
                conn.close()
                
        except Exception as e:
            logging.error(f"創建出貨記錄失敗: {str(e)}")
            raise Exception(f"創建出貨記錄失敗: {str(e)}")
    
    def update_shipment_status(
        self, 
        shipment_id: int, 
        status: int,
        operator_id: int,
        notes: Optional[str] = None
    ) -> bool:
        """
        更新出貨狀態
        
        Args:
            shipment_id: 出貨記錄ID
            status: 出貨狀態 (1: 包裝中, 2: 已出貨, 3: 已送達)
            operator_id: 操作員ID
            notes: 備註
            
        Returns:
            是否更新成功
        """
        try:
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 獲取當前狀態
            current_status_query = "SELECT status FROM shipments WHERE id = ?"
            current_status_result = self.db.execute_query(current_status_query, (shipment_id,))
            
            if not current_status_result:
                raise Exception(f"未找到ID為 {shipment_id} 的出貨記錄")
                
            current_status = current_status_result[0]['status']
            
            # 如果狀態未變化，直接返回成功
            if current_status == status:
                return True
            
            # 更新出貨狀態
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            try:
                # 開始事務
                conn.execute("BEGIN TRANSACTION")
                
                # 更新出貨狀態
                cursor.execute("""
                UPDATE shipments 
                SET status = ?, updated_at = ? 
                WHERE id = ?
                """, (status, now, shipment_id))
                
                # 獲取狀態對應的文字說明
                status_text = ["包裝中", "已出貨", "已送達"][status - 1]
                
                # 記錄操作日誌
                description = f"狀態更新為: {status_text}"
                if notes:
                    description += f", 備註: {notes}"
                    
                cursor.execute("""
                INSERT INTO shipment_logs (
                    shipment_id, action, description, operator_id, created_at
                ) VALUES (?, ?, ?, ?, ?)
                """, (
                    shipment_id, "更新狀態", description, operator_id, now
                ))
                
                # 提交事務
                conn.commit()
                
                return True
                
            except Exception as e:
                # 回滾事務
                conn.rollback()
                raise Exception(f"更新出貨狀態失敗: {str(e)}")
                
            finally:
                conn.close()
                
        except Exception as e:
            logging.error(f"更新出貨狀態失敗: {str(e)}")
            raise Exception(f"更新出貨狀態失敗: {str(e)}")
    
    def get_operators(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        獲取操作員列表
        
        Args:
            active_only: 是否只獲取活躍的操作員
            
        Returns:
            操作員列表
        """
        try:
            query = """
            SELECT id, username, display_name, is_admin, is_active
            FROM users
            """
            
            if active_only:
                query += " WHERE is_active = 1"
                
            results = self.db.execute_query(query)
            
            operators = []
            for row in results:
                operators.append(dict(row))
                
            return operators
            
        except Exception as e:
            logging.error(f"獲取操作員列表失敗: {str(e)}")
            raise Exception(f"獲取操作員列表失敗: {str(e)}")


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 