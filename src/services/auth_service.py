#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 認證服務

此模塊提供用戶認證和權限管理功能。
"""

import os
import sys
import json
import logging
import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from src.utils.auth_utils import AuthManager
from src.utils.db_utils import DBConnection


class AuthService:
    """
    認證服務類，提供用戶認證和會話管理功能
    """
    
    def __init__(self):
        """初始化認證服務"""
        self.db_conn = DBConnection()
        self.auth_manager = AuthManager(self.db_conn)
        
        # 配置目錄
        self.config_dir = self._get_config_dir()
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 用戶配置文件
        self.user_config_file = self.config_dir / 'user_config.json'
        
        # 當前用戶信息
        self.current_user = None
    
    def _get_config_dir(self) -> Path:
        """獲取配置目錄路徑"""
        # 從環境變量獲取數據目錄
        if 'SHIPMENT_RECORDER_DATA_DIR' in os.environ:
            return Path(os.environ['SHIPMENT_RECORDER_DATA_DIR']) / 'config'
        
        # 根據操作系統獲取適當的配置目錄
        if sys.platform == 'win32':
            app_data = os.environ.get('APPDATA', '')
            return Path(app_data) / 'ShipmentRecorder' / 'config'
        elif sys.platform == 'darwin':
            return Path.home() / 'Library' / 'Application Support' / 'ShipmentRecorder' / 'config'
        else:  # Linux and others
            return Path.home() / '.shipment_recorder' / 'config'
    
    def login(self, username: str, password: str, device_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        用戶登入
        
        Args:
            username: 用戶名
            password: 密碼
            device_info: 設備信息
            
        Returns:
            用戶信息字典，登入失敗返回None
        """
        try:
            # 調用認證管理器進行驗證
            success, message = self.auth_manager.login(username, password)
            
            if not success:
                logging.warning(f"用戶登入失敗: {message}")
                return None
            
            # 獲取用戶信息
            user_info = self.auth_manager.get_user_info()
            if not user_info:
                logging.error("無法獲取用戶信息")
                return None
            
            # 檢查訂閱狀態
            subscription_valid, message, expiry_date = self.auth_manager.check_subscription()
            if not subscription_valid:
                logging.warning(f"訂閱驗證失敗: {message}")
                return None
            
            # 設置當前用戶
            self.current_user = user_info
            self.current_user['subscription_expiry'] = expiry_date.isoformat() if expiry_date else None
            
            # 記錄設備信息
            self._save_device_login(user_info['id'], device_info)
            
            logging.info(f"用戶 {username} 登入成功")
            return self.current_user
        
        except Exception as e:
            logging.error(f"登入過程中發生錯誤: {str(e)}")
            return None
    
    def logout(self) -> bool:
        """
        用戶登出
        
        Returns:
            是否成功登出
        """
        try:
            success, message = self.auth_manager.logout()
            
            if success:
                self.current_user = None
                logging.info("用戶登出成功")
            else:
                logging.warning(f"用戶登出失敗: {message}")
            
            return success
        except Exception as e:
            logging.error(f"登出過程中發生錯誤: {str(e)}")
            return False
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """
        獲取當前登入用戶信息
        
        Returns:
            當前用戶信息字典，未登入則返回None
        """
        return self.current_user
    
    def is_admin(self) -> bool:
        """
        檢查當前用戶是否為管理員
        
        Returns:
            是否為管理員
        """
        if not self.current_user:
            return False
        
        return self.current_user.get('role') == 'admin'
    
    def get_saved_username(self) -> Optional[str]:
        """
        獲取保存的用戶名
        
        Returns:
            保存的用戶名，如果沒有則返回None
        """
        if not self.user_config_file.exists():
            return None
        
        try:
            with open(self.user_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('username')
        except Exception as e:
            logging.error(f"讀取用戶配置失敗: {str(e)}")
            return None
    
    def save_username(self, username: str) -> bool:
        """
        保存用戶名到配置文件
        
        Args:
            username: 要保存的用戶名
            
        Returns:
            是否保存成功
        """
        try:
            config = {}
            if self.user_config_file.exists():
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config['username'] = username
            
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False)
            
            return True
        except Exception as e:
            logging.error(f"保存用戶配置失敗: {str(e)}")
            return False
    
    def clear_saved_username(self) -> bool:
        """
        清除保存的用戶名
        
        Returns:
            是否清除成功
        """
        return self.save_username("")
    
    def _save_device_login(self, user_id: int, device_info: Dict[str, Any]) -> bool:
        """
        保存設備登入記錄
        
        Args:
            user_id: 用戶ID
            device_info: 設備信息
            
        Returns:
            是否保存成功
        """
        try:
            mac_address = device_info.get('mac_address', '')
            hostname = device_info.get('hostname', '')
            
            # 查詢設備是否已存在
            query = """
            SELECT id FROM devices 
            WHERE user_id = ? AND mac_address = ?
            """
            devices = self.db_conn.execute_query(query, (user_id, mac_address))
            
            now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if devices:
                # 更新設備登入時間
                update_query = """
                UPDATE devices 
                SET last_login = ?, device_name = ?
                WHERE user_id = ? AND mac_address = ?
                """
                self.db_conn.execute_update(update_query, (now, hostname, user_id, mac_address))
            else:
                # 新設備，添加記錄
                insert_query = """
                INSERT INTO devices (user_id, mac_address, device_name, last_login)
                VALUES (?, ?, ?, ?)
                """
                self.db_conn.execute_update(insert_query, (user_id, mac_address, hostname, now))
            
            return True
        except Exception as e:
            logging.error(f"保存設備登入記錄失敗: {str(e)}")
            return False
    
    def __del__(self):
        """析構函數，關閉數據庫連接"""
        if hasattr(self, 'db_conn'):
            self.db_conn.close()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 