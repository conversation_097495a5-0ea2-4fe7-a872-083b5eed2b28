#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 數據驗證工具

此模塊提供數據驗證功能，如輸入格式檢查、數據類型驗證等。
"""

import re
import sys
import logging
from typing import Optional, Any, Dict, List, Union


def validate_input(
    value: str,
    required: bool = True,
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    pattern: Optional[str] = None,
    error_msg: Optional[str] = None
) -> bool:
    """
    驗證輸入字符串
    
    Args:
        value: 要驗證的值
        required: 是否為必填項
        min_length: 最小長度
        max_length: 最大長度
        pattern: 正則表達式模式
        error_msg: 錯誤消息（用於日誌記錄）
        
    Returns:
        驗證是否通過
    """
    # 檢查必填項
    if required and not value:
        if error_msg:
            logging.warning(f"驗證失敗: {error_msg}")
        return False
    
    # 如果為空且非必填項，則通過驗證
    if not value and not required:
        return True
    
    # 檢查最小長度
    if min_length is not None and len(value) < min_length:
        if error_msg:
            logging.warning(f"驗證失敗: {error_msg}")
        return False
    
    # 檢查最大長度
    if max_length is not None and len(value) > max_length:
        if error_msg:
            logging.warning(f"驗證失敗: {error_msg}")
        return False
    
    # 檢查正則表達式模式
    if pattern and not re.match(pattern, value):
        if error_msg:
            logging.warning(f"驗證失敗: {error_msg}")
        return False
    
    return True


def validate_email(email: str, required: bool = True) -> bool:
    """
    驗證電子郵件格式
    
    Args:
        email: 電子郵件地址
        required: 是否為必填項
        
    Returns:
        驗證是否通過
    """
    email_pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    return validate_input(
        email,
        required=required,
        pattern=email_pattern,
        error_msg="電子郵件格式不正確"
    )


def validate_password(password: str, min_length: int = 8) -> bool:
    """
    驗證密碼強度
    
    Args:
        password: 密碼
        min_length: 最小長度
        
    Returns:
        驗證是否通過
    """
    # 至少包含一個大寫字母、一個小寫字母、一個數字和一個特殊字符
    password_pattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$'
    
    return validate_input(
        password,
        min_length=min_length,
        pattern=password_pattern,
        error_msg="密碼強度不足"
    )


def validate_date(date_str: str, format: str = '%Y-%m-%d') -> bool:
    """
    驗證日期格式
    
    Args:
        date_str: 日期字符串
        format: 日期格式
        
    Returns:
        驗證是否通過
    """
    import datetime
    
    try:
        datetime.datetime.strptime(date_str, format)
        return True
    except ValueError:
        logging.warning(f"日期格式不正確: {date_str}")
        return False


def validate_number(
    value: Union[str, int, float],
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    is_integer: bool = False
) -> bool:
    """
    驗證數字
    
    Args:
        value: 要驗證的值
        min_value: 最小值
        max_value: 最大值
        is_integer: 是否為整數
        
    Returns:
        驗證是否通過
    """
    try:
        if isinstance(value, str):
            if is_integer:
                num_value = int(value)
            else:
                num_value = float(value)
        else:
            num_value = value
            
        if is_integer and not isinstance(num_value, int):
            logging.warning(f"值不是整數: {value}")
            return False
            
        if min_value is not None and num_value < min_value:
            logging.warning(f"值小於最小值: {value} < {min_value}")
            return False
            
        if max_value is not None and num_value > max_value:
            logging.warning(f"值大於最大值: {value} > {max_value}")
            return False
            
        return True
    except (ValueError, TypeError):
        logging.warning(f"無法解析為數字: {value}")
        return False


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 