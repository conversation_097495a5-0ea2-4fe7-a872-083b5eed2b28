#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 硬件工具

此模塊提供硬件相關的功能，如獲取設備信息和MAC地址。
"""

import os
import platform
import uuid
import re
import logging
import socket
import json
from typing import Dict, Any, Optional


def get_mac_address() -> str:
    """
    獲取MAC地址
    
    Returns:
        MAC地址字符串，如果無法獲取，則返回唯一標識符
    """
    try:
        # 嘗試獲取主要網絡接口的MAC地址
        mac = uuid.getnode()
        mac_str = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) for elements in range(0, 8*6, 8)][::-1])
        
        # 檢查MAC地址格式是否正確
        if re.match(r'^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$', mac_str):
            return mac_str
        
        # 如果格式不正確，則使用備用方法
        return _get_mac_address_fallback()
    
    except Exception as e:
        logging.warning(f"無法獲取MAC地址: {str(e)}，使用備用方法")
        return _get_mac_address_fallback()


def _get_mac_address_fallback() -> str:
    """
    備用方法獲取MAC地址
    
    Returns:
        格式化的唯一標識符
    """
    # 生成一個基於主機名和UUID的唯一標識符
    host_id = socket.gethostname() + str(uuid.uuid4())
    # 將唯一標識符轉換為類似MAC地址的格式
    fake_mac = uuid.uuid5(uuid.NAMESPACE_DNS, host_id).hex[-12:]
    return ':'.join([fake_mac[i:i+2] for i in range(0, 12, 2)])


def get_device_info() -> Dict[str, Any]:
    """
    獲取設備信息
    
    Returns:
        設備信息字典
    """
    device_info = {
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'hostname': socket.gethostname(),
        'username': os.getlogin() if hasattr(os, 'getlogin') else 'unknown',
        'mac_address': get_mac_address()
    }
    
    # 如果是Windows系統，添加更多特定信息
    if platform.system() == 'Windows':
        try:
            import wmi
            c = wmi.WMI()
            
            # 獲取BIOS序列號
            for bios in c.Win32_BIOS():
                device_info['bios_serial'] = bios.SerialNumber if bios.SerialNumber else 'unknown'
            
            # 獲取硬碟序列號
            for disk in c.Win32_DiskDrive():
                device_info['disk_serial'] = disk.SerialNumber.strip() if disk.SerialNumber else 'unknown'
                break  # 只獲取第一個硬碟的信息
        
        except ImportError:
            logging.warning("無法導入wmi模塊，部分Windows系統信息將不可用")
        except Exception as e:
            logging.warning(f"獲取Windows特定信息時發生錯誤: {str(e)}")
    
    # 如果是MacOS系統，添加更多特定信息
    elif platform.system() == 'Darwin':
        try:
            # 獲取設備序列號
            import subprocess
            serial = subprocess.check_output(['system_profiler', 'SPHardwareDataType']).decode('utf-8')
            match = re.search(r'Hardware UUID: (.+)', serial)
            if match:
                device_info['hardware_uuid'] = match.group(1).strip()
        
        except Exception as e:
            logging.warning(f"獲取MacOS特定信息時發生錯誤: {str(e)}")
    
    # 如果是Linux系統，添加更多特定信息
    elif platform.system() == 'Linux':
        try:
            # 獲取設備序列號
            if os.path.exists('/etc/machine-id'):
                with open('/etc/machine-id', 'r') as f:
                    device_info['machine_id'] = f.read().strip()
        
        except Exception as e:
            logging.warning(f"獲取Linux特定信息時發生錯誤: {str(e)}")
    
    return device_info


def generate_device_id(mac_address: Optional[str] = None) -> str:
    """
    生成設備ID
    
    Args:
        mac_address: MAC地址，如果為None則獲取當前設備的MAC地址
    
    Returns:
        設備ID
    """
    if not mac_address:
        mac_address = get_mac_address()
    
    # 使用MAC地址生成一個簡單的ID
    clean_mac = mac_address.replace(':', '')
    device_id = clean_mac
    
    return device_id


def check_device_id(device_id: str) -> bool:
    """
    檢查設備ID是否有效
    
    Args:
        device_id: 要檢查的設備ID
    
    Returns:
        設備ID是否有效
    """
    # 檢查設備ID是否為空
    if not device_id:
        return False
    
    # 檢查設備ID是否為MAC地址格式
    if ':' in device_id and re.match(r'^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$', device_id):
        return True
    
    # 檢查設備ID是否為沒有冒號的MAC地址格式
    if re.match(r'^[0-9A-Fa-f]{12}$', device_id):
        return True
    
    # 如果設備ID不符合上述格式，但長度合適，也認為是有效的
    if len(device_id) >= 10:
        return True
    
    return False


def save_device_info_to_file(file_path: str) -> bool:
    """
    將設備信息保存到文件
    
    Args:
        file_path: 文件路徑
    
    Returns:
        是否保存成功
    """
    try:
        device_info = get_device_info()
        
        # 確保目錄存在
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
        
        # 寫入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(device_info, f, indent=4, ensure_ascii=False)
        
        return True
    
    except Exception as e:
        logging.error(f"保存設備信息到文件時發生錯誤: {str(e)}")
        return False


def load_device_info_from_file(file_path: str) -> Optional[Dict[str, Any]]:
    """
    從文件加載設備信息
    
    Args:
        file_path: 文件路徑
    
    Returns:
        設備信息字典，如果加載失敗則返回None
    """
    try:
        if not os.path.exists(file_path):
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            device_info = json.load(f)
        
        return device_info
    
    except Exception as e:
        logging.error(f"從文件加載設備信息時發生錯誤: {str(e)}")
        return None


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    import sys
    sys.exit(1) 