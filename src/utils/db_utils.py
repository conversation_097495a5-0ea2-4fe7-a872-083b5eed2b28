#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 數據庫工具

此模塊提供數據庫連接和操作的功能。
"""

import os
import sys
import sqlite3
import logging
from typing import Optional, List, Dict, Any, Tuple, Union


class DBConnection:
    """
    數據庫連接類，處理SQLite數據庫的連接和操作
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        初始化數據庫連接
        
        Args:
            db_path: 數據庫文件路徑，如果為None則使用默認路徑
        """
        if db_path is None:
            # 獲取項目根目錄
            root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            # 使用默認數據庫路徑
            db_path = os.path.join(root_dir, 'database', 'database.sqlite')
            
        # 確保數據庫目錄存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        self.db_path = db_path
        self.connection = None
        
        try:
            # 連接數據庫
            self._connect()
            
            # 初始化數據庫表結構
            if not self._check_tables_exist():
                self._initialize_database()
        except Exception as e:
            logging.error(f"數據庫初始化失敗: {str(e)}")
            raise
    
    def _connect(self):
        """
        連接到SQLite數據庫
        """
        try:
            self.connection = sqlite3.connect(self.db_path)
            # 設置行工廠以返回字典而不是元組
            self.connection.row_factory = sqlite3.Row
            # 啟用外鍵約束
            self.connection.execute("PRAGMA foreign_keys = ON")
        except sqlite3.Error as e:
            logging.error(f"數據庫連接失敗: {str(e)}")
            raise
    
    def _check_tables_exist(self) -> bool:
        """
        檢查必要的表是否存在
        
        Returns:
            布爾值表示所有必要的表是否存在
        """
        required_tables = [
            'users', 'subscription', 'devices', 'password_reset',
            'shipments', 'shipment_items', 'customers', 'products'
        ]
        
        try:
            # 獲取所有表名
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row['name'] for row in cursor.fetchall()]
            
            # 檢查所有必要的表是否存在
            for table in required_tables:
                if table not in existing_tables:
                    return False
            
            return True
        except sqlite3.Error as e:
            logging.error(f"檢查表存在失敗: {str(e)}")
            return False
    
    def _initialize_database(self):
        """
        初始化數據庫表結構
        """
        try:
            cursor = self.connection.cursor()
            
            # 創建用戶表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT NOT NULL UNIQUE,
                role TEXT NOT NULL DEFAULT 'user',
                created_at TEXT NOT NULL,
                last_login TEXT,
                is_active INTEGER NOT NULL DEFAULT 1
            )
            ''')
            
            # 創建訂閱表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS subscription (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                start_date TEXT NOT NULL,
                expiry_date TEXT NOT NULL,
                is_active INTEGER NOT NULL DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            ''')
            
            # 創建設備表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                mac_address TEXT NOT NULL,
                device_name TEXT NOT NULL,
                last_login TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE (user_id, mac_address)
            )
            ''')
            
            # 創建密碼重置表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS password_reset (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                reset_token TEXT NOT NULL UNIQUE,
                expiry_date TEXT NOT NULL,
                is_used INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            ''')
            
            # 創建客戶表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            ''')
            
            # 創建產品表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                sku TEXT NOT NULL UNIQUE,
                barcode TEXT,
                description TEXT,
                unit TEXT,
                created_by INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            ''')
            
            # 創建出貨表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS shipments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shipment_number TEXT NOT NULL UNIQUE,
                customer_id INTEGER NOT NULL,
                shipment_date TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            ''')
            
            # 創建出貨項目表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS shipment_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shipment_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                serial_number TEXT,
                notes TEXT,
                created_at TEXT NOT NULL,
                FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
            ''')
            
            # 創建管理員賬戶
            cursor.execute('''
            INSERT OR IGNORE INTO users (
                username, password, full_name, email, role, created_at, is_active
            ) VALUES (
                'admin', 
                '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', 
                '系統管理員', 
                '<EMAIL>', 
                'admin', 
                datetime('now', 'localtime'), 
                1
            )
            ''')
            
            # 提交更改
            self.connection.commit()
            logging.info("數據庫初始化成功")
        except sqlite3.Error as e:
            self.connection.rollback()
            logging.error(f"數據庫初始化失敗: {str(e)}")
            raise
    
    def close(self):
        """
        關閉數據庫連接
        """
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: Optional[Union[Tuple, List, Dict]] = None) -> List[Dict[str, Any]]:
        """
        執行SQL查詢
        
        Args:
            query: SQL查詢語句
            params: 查詢參數
            
        Returns:
            查詢結果列表，每個元素為一個字典
        """
        if not self.connection:
            self._connect()
        
        try:
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            result = cursor.fetchall()
            
            # 將Row對象轉換為字典
            return [dict(row) for row in result]
        except sqlite3.Error as e:
            logging.error(f"執行查詢失敗: {str(e)}, 查詢: {query}, 參數: {params}")
            raise
    
    def execute_update(self, query: str, params: Optional[Union[Tuple, List, Dict]] = None) -> int:
        """
        執行SQL更新（INSERT, UPDATE, DELETE）
        
        Args:
            query: SQL更新語句
            params: 更新參數
            
        Returns:
            受影響的行數
        """
        if not self.connection:
            self._connect()
        
        try:
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            return cursor.rowcount
        except sqlite3.Error as e:
            self.connection.rollback()
            logging.error(f"執行更新失敗: {str(e)}, 查詢: {query}, 參數: {params}")
            raise
    
    def get_last_insert_id(self) -> int:
        """
        獲取最後插入的行ID
        
        Returns:
            最後插入的行ID
        """
        if not self.connection:
            self._connect()
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT last_insert_rowid()")
            return cursor.fetchone()[0]
        except sqlite3.Error as e:
            logging.error(f"獲取最後插入ID失敗: {str(e)}")
            raise
    
    def begin_transaction(self):
        """
        開始事務
        """
        if not self.connection:
            self._connect()
        
        try:
            self.connection.execute("BEGIN TRANSACTION")
        except sqlite3.Error as e:
            logging.error(f"開始事務失敗: {str(e)}")
            raise
    
    def commit(self):
        """
        提交事務
        """
        if not self.connection:
            return
        
        try:
            self.connection.commit()
        except sqlite3.Error as e:
            logging.error(f"提交事務失敗: {str(e)}")
            self.connection.rollback()
            raise
    
    def rollback(self):
        """
        回滾事務
        """
        if not self.connection:
            return
        
        try:
            self.connection.rollback()
        except sqlite3.Error as e:
            logging.error(f"回滾事務失敗: {str(e)}")
            raise
    
    def table_exists(self, table_name: str) -> bool:
        """
        檢查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            表是否存在
        """
        if not self.connection:
            self._connect()
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table_name,)
            )
            return bool(cursor.fetchone())
        except sqlite3.Error as e:
            logging.error(f"檢查表是否存在失敗: {str(e)}")
            raise
    
    def backup_database(self, backup_path: str) -> bool:
        """
        備份數據庫
        
        Args:
            backup_path: 備份文件路徑
            
        Returns:
            備份是否成功
        """
        if not self.connection:
            self._connect()
        
        try:
            # 確保備份目錄存在
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # 創建備份連接
            backup_connection = sqlite3.connect(backup_path)
            
            # 執行備份
            self.connection.backup(backup_connection)
            
            # 關閉備份連接
            backup_connection.close()
            
            logging.info(f"數據庫已備份到: {backup_path}")
            return True
        except sqlite3.Error as e:
            logging.error(f"數據庫備份失敗: {str(e)}")
            return False
    
    def __enter__(self):
        """
        上下文管理器入口
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口
        """
        self.close()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 