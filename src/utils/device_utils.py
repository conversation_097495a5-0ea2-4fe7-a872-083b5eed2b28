#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 設備資訊工具

此模塊提供獲取設備信息的功能，如MAC地址、計算機名稱等。
"""

import os
import sys
import uuid
import socket
import platform
import logging
from typing import Dict, Any, Optional, List


class DeviceInfo:
    """
    設備信息類，用於獲取並儲存設備的相關信息
    """
    
    def __init__(self):
        """初始化設備信息類"""
        self._mac_address = None
        self._hostname = None
        self._os_info = None
        self._platform_info = None
        self._cpu_info = None
        self._python_version = None
        
        # 初始化加載所有設備信息
        self._load_device_info()
    
    def _load_device_info(self) -> None:
        """加載設備信息"""
        try:
            self._mac_address = self.get_mac_address()
            self._hostname = self.get_hostname()
            self._os_info = self.get_os_info()
            self._platform_info = self.get_platform_info()
            self._cpu_info = self.get_cpu_info()
            self._python_version = self.get_python_version()
        except Exception as e:
            logging.error(f"加載設備信息失敗: {str(e)}")
    
    @staticmethod
    def get_mac_address() -> str:
        """
        獲取MAC地址
        
        Returns:
            MAC地址字符串
        """
        mac = uuid.getnode()
        mac_hex = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) for elements in range(0, 48, 8)][::-1])
        return mac_hex
    
    @staticmethod
    def get_all_mac_addresses() -> List[str]:
        """
        獲取所有網絡接口的MAC地址
        
        Returns:
            所有MAC地址的列表
        """
        try:
            import netifaces
            
            mac_addresses = []
            interfaces = netifaces.interfaces()
            
            for interface in interfaces:
                try:
                    mac = netifaces.ifaddresses(interface)[netifaces.AF_LINK][0]['addr']
                    if mac and mac != '00:00:00:00:00:00':
                        mac_addresses.append(mac)
                except (KeyError, IndexError):
                    pass
            
            return mac_addresses
        except ImportError:
            # 如果netifaces不可用，返回使用uuid獲取的MAC地址
            return [DeviceInfo.get_mac_address()]
    
    @staticmethod
    def get_hostname() -> str:
        """
        獲取主機名
        
        Returns:
            主機名字符串
        """
        return socket.gethostname()
    
    @staticmethod
    def get_os_info() -> Dict[str, str]:
        """
        獲取操作系統信息
        
        Returns:
            操作系統信息字典
        """
        os_info = {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor()
        }
        return os_info
    
    @staticmethod
    def get_platform_info() -> str:
        """
        獲取平台信息
        
        Returns:
            平台信息字符串
        """
        return platform.platform()
    
    @staticmethod
    def get_cpu_info() -> Dict[str, Any]:
        """
        獲取CPU信息
        
        Returns:
            CPU信息字典
        """
        import multiprocessing
        
        cpu_info = {
            'physical_cores': multiprocessing.cpu_count(),
            'logical_cores': os.cpu_count(),
            'processor': platform.processor()
        }
        
        return cpu_info
    
    @staticmethod
    def get_python_version() -> str:
        """
        獲取Python版本
        
        Returns:
            Python版本字符串
        """
        return platform.python_version()
    
    def get_device_fingerprint(self) -> str:
        """
        獲取設備指紋，用於唯一標識設備
        
        Returns:
            設備指紋字符串
        """
        import hashlib
        
        # 組合多個設備特徵生成指紋
        fingerprint_data = (
            self.mac_address + 
            self.hostname + 
            self.os_info.get('system', '') + 
            self.os_info.get('machine', '') + 
            platform.node()
        )
        
        # 使用SHA-256生成指紋
        fingerprint = hashlib.sha256(fingerprint_data.encode('utf-8')).hexdigest()
        
        return fingerprint
    
    def get_all_info(self) -> Dict[str, Any]:
        """
        獲取所有設備信息
        
        Returns:
            所有設備信息的字典
        """
        return {
            'mac_address': self.mac_address,
            'all_mac_addresses': self.get_all_mac_addresses(),
            'hostname': self.hostname,
            'os_info': self.os_info,
            'platform_info': self.platform_info,
            'cpu_info': self.cpu_info,
            'python_version': self.python_version,
            'device_fingerprint': self.get_device_fingerprint()
        }
    
    @property
    def mac_address(self) -> str:
        """獲取MAC地址屬性"""
        if self._mac_address is None:
            self._mac_address = self.get_mac_address()
        return self._mac_address
    
    @property
    def hostname(self) -> str:
        """獲取主機名屬性"""
        if self._hostname is None:
            self._hostname = self.get_hostname()
        return self._hostname
    
    @property
    def os_info(self) -> Dict[str, str]:
        """獲取操作系統信息屬性"""
        if self._os_info is None:
            self._os_info = self.get_os_info()
        return self._os_info
    
    @property
    def platform_info(self) -> str:
        """獲取平台信息屬性"""
        if self._platform_info is None:
            self._platform_info = self.get_platform_info()
        return self._platform_info
    
    @property
    def cpu_info(self) -> Dict[str, Any]:
        """獲取CPU信息屬性"""
        if self._cpu_info is None:
            self._cpu_info = self.get_cpu_info()
        return self._cpu_info
    
    @property
    def python_version(self) -> str:
        """獲取Python版本屬性"""
        if self._python_version is None:
            self._python_version = self.get_python_version()
        return self._python_version


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 