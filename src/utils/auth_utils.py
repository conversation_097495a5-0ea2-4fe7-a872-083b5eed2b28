#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 認證工具

此模塊提供用戶登入認證和訂閱狀態檢查的功能。
"""

import os
import sys
import logging
import hashlib
import datetime
from typing import Optional, Tuple, Dict, Any

# 導入本地模塊
from src.utils.db_utils import DBConnection
from src.utils.device_utils import get_device_info, get_mac_address

class AuthManager:
    """
    認證管理器類，處理用戶登入和訂閱狀態
    """
    
    def __init__(self, db_conn: Optional[DBConnection] = None):
        """
        初始化認證管理器
        
        Args:
            db_conn: 數據庫連接對象，如果為None則創建新連接
        """
        self.db = db_conn if db_conn else DBConnection()
        self.current_user = None
        self.device_info = get_device_info()
        self.logged_in = False
        self.is_admin = False
        
    def login(self, username: str, password: str) -> Tu<PERSON>[bool, str]:
        """
        用戶登入
        
        Args:
            username: 用戶名
            password: 密碼
            
        Returns:
            (成功狀態, 消息): 元組包含登入是否成功及相關消息
        """
        try:
            # 對密碼進行哈希處理
            hashed_password = self._hash_password(password)
            
            # 查詢用戶
            query = """
            SELECT u.*, s.expiry_date, s.is_active
            FROM users u
            LEFT JOIN subscription s ON u.id = s.user_id
            WHERE u.username = ? AND u.password = ?
            """
            result = self.db.execute_query(query, (username, hashed_password))
            
            if not result:
                return False, "用戶名或密碼錯誤"
            
            user_data = result[0]
            user_id = user_data['id']
            
            # 檢查用戶是否被禁用
            if not user_data['is_active']:
                return False, "此帳戶已被禁用，請聯繫管理員"
            
            # 檢查訂閱狀態
            if user_data['role'] != 'admin':  # 管理員不需要檢查訂閱
                subscription_active = user_data.get('is_active', 0)
                expiry_date_str = user_data.get('expiry_date')
                
                if not subscription_active:
                    return False, "您的訂閱未激活，請聯繫管理員"
                
                if expiry_date_str:
                    expiry_date = datetime.datetime.strptime(expiry_date_str, "%Y-%m-%d").date()
                    today = datetime.date.today()
                    
                    if today > expiry_date:
                        return False, f"您的訂閱已於 {expiry_date} 過期"
            
            # 檢查設備登入狀態
            mac_address = self.device_info['mac_address']
            existing_device = self.db.execute_query(
                "SELECT * FROM devices WHERE user_id = ? AND mac_address = ?",
                (user_id, mac_address)
            )
            
            if not existing_device:
                # 查詢用戶的設備數量
                device_count = self.db.execute_query(
                    "SELECT COUNT(*) as count FROM devices WHERE user_id = ?",
                    (user_id,)
                )[0]['count']
                
                # 如果不是管理員且已有一個設備登入，則拒絕登入
                if user_data['role'] != 'admin' and device_count >= 1:
                    return False, "您的帳戶已在其他設備登入，請先登出"
                
                # 添加新設備記錄
                self.db.execute_update(
                    """INSERT INTO devices (user_id, mac_address, device_name, last_login) 
                    VALUES (?, ?, ?, datetime('now', 'localtime'))""",
                    (user_id, mac_address, self.device_info['computer_name'])
                )
            else:
                # 更新設備最後登入時間
                self.db.execute_update(
                    """UPDATE devices SET last_login = datetime('now', 'localtime')
                    WHERE user_id = ? AND mac_address = ?""",
                    (user_id, mac_address)
                )
            
            # 更新用戶最後登入時間
            self.db.execute_update(
                "UPDATE users SET last_login = datetime('now', 'localtime') WHERE id = ?",
                (user_id,)
            )
            
            # 設置當前用戶
            self.current_user = {
                'id': user_id,
                'username': username,
                'full_name': user_data['full_name'],
                'email': user_data['email'],
                'role': user_data['role'],
                'created_at': user_data['created_at'],
                'is_active': user_data['is_active']
            }
            
            self.logged_in = True
            self.is_admin = (user_data['role'] == 'admin')
            
            return True, f"歡迎回來，{user_data['full_name']}"
            
        except Exception as e:
            logging.error(f"登入失敗: {str(e)}")
            return False, f"登入過程發生錯誤: {str(e)}"
    
    def logout(self) -> Tuple[bool, str]:
        """
        用戶登出
        
        Returns:
            (成功狀態, 消息): 元組包含登出是否成功及相關消息
        """
        try:
            if not self.logged_in or not self.current_user:
                return False, "您尚未登入"
            
            # 清除當前用戶數據
            self.current_user = None
            self.logged_in = False
            self.is_admin = False
            
            return True, "已成功登出"
        except Exception as e:
            logging.error(f"登出失敗: {str(e)}")
            return False, f"登出過程發生錯誤: {str(e)}"
    
    def register(self, username: str, password: str, full_name: str, 
                 email: str, role: str = 'user') -> Tuple[bool, str]:
        """
        注冊新用戶 (僅限管理員使用)
        
        Args:
            username: 用戶名
            password: 密碼
            full_name: 用戶全名
            email: 電子郵件
            role: 用戶角色，默認為'user'
            
        Returns:
            (成功狀態, 消息): 元組包含注冊是否成功及相關消息
        """
        if not self.is_admin:
            return False, "只有管理員可以注冊新用戶"
        
        try:
            # 檢查用戶名是否已存在
            existing_user = self.db.execute_query(
                "SELECT id FROM users WHERE username = ?",
                (username,)
            )
            
            if existing_user:
                return False, "用戶名已存在"
            
            # 檢查郵箱是否已存在
            existing_email = self.db.execute_query(
                "SELECT id FROM users WHERE email = ?",
                (email,)
            )
            
            if existing_email:
                return False, "該電子郵件已被注冊"
            
            # 對密碼進行哈希處理
            hashed_password = self._hash_password(password)
            
            # 插入新用戶
            self.db.execute_update(
                """INSERT INTO users 
                (username, password, full_name, email, role, created_at, is_active)
                VALUES (?, ?, ?, ?, ?, datetime('now', 'localtime'), 1)""",
                (username, hashed_password, full_name, email, role)
            )
            
            # 獲取新用戶ID
            new_user = self.db.execute_query(
                "SELECT id FROM users WHERE username = ?",
                (username,)
            )
            
            if not new_user:
                return False, "用戶創建失敗"
            
            user_id = new_user[0]['id']
            
            # 為新用戶創建訂閱記錄
            if role != 'admin':
                # 設置訂閱期限為30天後
                expiry_date = (datetime.date.today() + datetime.timedelta(days=30)).strftime("%Y-%m-%d")
                
                self.db.execute_update(
                    """INSERT INTO subscription 
                    (user_id, start_date, expiry_date, is_active)
                    VALUES (?, date('now', 'localtime'), ?, 1)""",
                    (user_id, expiry_date)
                )
            
            return True, f"用戶 {username} 創建成功"
            
        except Exception as e:
            logging.error(f"用戶注冊失敗: {str(e)}")
            return False, f"用戶注冊過程發生錯誤: {str(e)}"
    
    def change_password(self, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        更改用戶密碼
        
        Args:
            old_password: 舊密碼
            new_password: 新密碼
            
        Returns:
            (成功狀態, 消息): 元組包含密碼更改是否成功及相關消息
        """
        if not self.logged_in or not self.current_user:
            return False, "您需要先登入"
        
        try:
            # 檢查舊密碼是否正確
            hashed_old_password = self._hash_password(old_password)
            
            user_check = self.db.execute_query(
                "SELECT id FROM users WHERE id = ? AND password = ?",
                (self.current_user['id'], hashed_old_password)
            )
            
            if not user_check:
                return False, "舊密碼不正確"
            
            # 對新密碼進行哈希處理
            hashed_new_password = self._hash_password(new_password)
            
            # 更新密碼
            self.db.execute_update(
                "UPDATE users SET password = ? WHERE id = ?",
                (hashed_new_password, self.current_user['id'])
            )
            
            return True, "密碼已成功更新"
            
        except Exception as e:
            logging.error(f"更改密碼失敗: {str(e)}")
            return False, f"更改密碼過程發生錯誤: {str(e)}"
    
    def reset_password(self, username: str, email: str) -> Tuple[bool, str]:
        """
        重置用戶密碼 (生成一個重置令牌)
        
        Args:
            username: 用戶名
            email: 電子郵件
            
        Returns:
            (成功狀態, 消息): 元組包含重置過程是否成功及相關消息
        """
        try:
            # 檢查用戶名和郵箱是否匹配
            user = self.db.execute_query(
                "SELECT id, email FROM users WHERE username = ? AND email = ?",
                (username, email)
            )
            
            if not user:
                return False, "用戶名或郵箱不正確"
            
            user_id = user[0]['id']
            
            # 生成一個重置令牌
            token = hashlib.sha256(f"{username}{datetime.datetime.now().timestamp()}".encode()).hexdigest()
            
            # 令牌有效期設為24小時
            expiry = (datetime.datetime.now() + datetime.timedelta(hours=24)).strftime("%Y-%m-%d %H:%M:%S")
            
            # 清除該用戶的舊令牌
            self.db.execute_update(
                "DELETE FROM password_reset WHERE user_id = ?",
                (user_id,)
            )
            
            # 插入新令牌
            self.db.execute_update(
                """INSERT INTO password_reset (user_id, reset_token, expiry_date, is_used)
                VALUES (?, ?, ?, 0)""",
                (user_id, token, expiry)
            )
            
            # 在實際應用中，這裡應該發送一封含有重置鏈接的電子郵件
            # 但在此示例中，我們只返回令牌用於測試
            return True, f"密碼重置請求已處理。令牌: {token}"
            
        except Exception as e:
            logging.error(f"重置密碼失敗: {str(e)}")
            return False, f"重置密碼過程發生錯誤: {str(e)}"
    
    def complete_reset_password(self, token: str, new_password: str) -> Tuple[bool, str]:
        """
        完成密碼重置過程
        
        Args:
            token: 重置令牌
            new_password: 新密碼
            
        Returns:
            (成功狀態, 消息): 元組包含重置是否成功及相關消息
        """
        try:
            # 查找令牌
            reset_info = self.db.execute_query(
                """SELECT r.user_id, r.expiry_date, r.is_used, u.username
                FROM password_reset r
                JOIN users u ON r.user_id = u.id
                WHERE r.reset_token = ?""",
                (token,)
            )
            
            if not reset_info:
                return False, "無效的重置令牌"
            
            reset_data = reset_info[0]
            
            # 檢查令牌是否已使用
            if reset_data['is_used']:
                return False, "此令牌已被使用"
            
            # 檢查令牌是否過期
            expiry_date = datetime.datetime.strptime(reset_data['expiry_date'], "%Y-%m-%d %H:%M:%S")
            if datetime.datetime.now() > expiry_date:
                return False, "此令牌已過期"
            
            # 對新密碼進行哈希處理
            hashed_new_password = self._hash_password(new_password)
            
            # 更新用戶密碼
            self.db.execute_update(
                "UPDATE users SET password = ? WHERE id = ?",
                (hashed_new_password, reset_data['user_id'])
            )
            
            # 標記令牌為已使用
            self.db.execute_update(
                "UPDATE password_reset SET is_used = 1 WHERE reset_token = ?",
                (token,)
            )
            
            return True, f"密碼已成功重置，請使用新密碼登入"
            
        except Exception as e:
            logging.error(f"完成密碼重置失敗: {str(e)}")
            return False, f"完成密碼重置過程發生錯誤: {str(e)}"
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        獲取當前登入用戶的信息
        
        Returns:
            用戶信息字典，如果未登入則返回None
        """
        if not self.logged_in or not self.current_user:
            return None
        
        # 如果需要獲取最新的用戶數據，可以在這裡從數據庫重新查詢
        return self.current_user
    
    def check_subscription(self) -> Tuple[bool, str, Optional[datetime.date]]:
        """
        檢查當前用戶的訂閱狀態
        
        Returns:
            (是否有效, 狀態消息, 過期日期): 元組包含訂閱是否有效、狀態消息和過期日期
        """
        if not self.logged_in or not self.current_user:
            return False, "您需要先登入", None
        
        # 管理員始終有權限
        if self.is_admin:
            return True, "管理員賬戶", None
        
        try:
            # 查詢用戶的訂閱信息
            subscription = self.db.execute_query(
                "SELECT * FROM subscription WHERE user_id = ?",
                (self.current_user['id'],)
            )
            
            if not subscription:
                return False, "未找到訂閱信息", None
            
            sub_data = subscription[0]
            
            # 檢查訂閱是否激活
            if not sub_data['is_active']:
                return False, "訂閱未激活", None
            
            # 檢查訂閱是否過期
            expiry_date = datetime.datetime.strptime(sub_data['expiry_date'], "%Y-%m-%d").date()
            today = datetime.date.today()
            
            if today > expiry_date:
                return False, f"訂閱已於 {expiry_date} 過期", expiry_date
            
            # 計算剩餘天數
            days_left = (expiry_date - today).days
            
            if days_left <= 7:
                return True, f"訂閱即將到期，剩餘 {days_left} 天", expiry_date
            
            return True, f"訂閱有效，到期日期: {expiry_date}", expiry_date
            
        except Exception as e:
            logging.error(f"檢查訂閱失敗: {str(e)}")
            return False, f"檢查訂閱過程發生錯誤: {str(e)}", None
    
    def extend_subscription(self, user_id: int, days: int) -> Tuple[bool, str]:
        """
        延長用戶的訂閱期限 (僅限管理員使用)
        
        Args:
            user_id: 用戶ID
            days: 延長的天數
            
        Returns:
            (成功狀態, 消息): 元組包含操作是否成功及相關消息
        """
        if not self.is_admin:
            return False, "只有管理員可以延長訂閱"
        
        try:
            # 獲取用戶的當前訂閱
            subscription = self.db.execute_query(
                "SELECT * FROM subscription WHERE user_id = ?",
                (user_id,)
            )
            
            if not subscription:
                # 用戶沒有訂閱記錄，創建一個新的
                expiry_date = (datetime.date.today() + datetime.timedelta(days=days)).strftime("%Y-%m-%d")
                
                self.db.execute_update(
                    """INSERT INTO subscription 
                    (user_id, start_date, expiry_date, is_active)
                    VALUES (?, date('now', 'localtime'), ?, 1)""",
                    (user_id, expiry_date)
                )
                
                return True, f"已為用戶 ID: {user_id} 創建 {days} 天的新訂閱"
            
            sub_data = subscription[0]
            
            # 從當前過期日期或今天開始計算新的過期日期
            current_expiry = datetime.datetime.strptime(sub_data['expiry_date'], "%Y-%m-%d").date()
            today = datetime.date.today()
            
            # 如果訂閱已過期，則從今天開始計算
            if today > current_expiry:
                start_date = today
            else:
                start_date = current_expiry
            
            # 計算新的過期日期
            new_expiry = (start_date + datetime.timedelta(days=days)).strftime("%Y-%m-%d")
            
            # 更新訂閱記錄
            self.db.execute_update(
                """UPDATE subscription 
                SET expiry_date = ?, is_active = 1
                WHERE user_id = ?""",
                (new_expiry, user_id)
            )
            
            return True, f"用戶 ID: {user_id} 的訂閱已延長 {days} 天，新的過期日期為 {new_expiry}"
            
        except Exception as e:
            logging.error(f"延長訂閱失敗: {str(e)}")
            return False, f"延長訂閱過程發生錯誤: {str(e)}"
    
    def _hash_password(self, password: str) -> str:
        """
        對密碼進行哈希處理
        
        Args:
            password: 明文密碼
            
        Returns:
            哈希後的密碼
        """
        # 在實際應用中應使用更安全的哈希方法，如bcrypt
        # 這裡使用簡單的SHA-256作為示例
        return hashlib.sha256(password.encode()).hexdigest()


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 