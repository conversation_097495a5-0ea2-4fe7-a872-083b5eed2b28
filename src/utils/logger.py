#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 日誌工具

此模塊提供日誌配置和管理功能。
"""

import os
import sys
import logging
import datetime
from pathlib import Path
from typing import Optional, Union


def setup_logger(
    log_file: Optional[Union[str, Path]] = None,
    log_level: int = logging.INFO,
    console_level: int = logging.INFO,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    設置日誌記錄器
    
    Args:
        log_file: 日誌文件路徑，如果為None，則僅輸出到控制台
        log_level: 日誌文件的記錄級別
        console_level: 控制台的記錄級別
        format_string: 自定義的日誌格式
        
    Returns:
        配置好的日誌記錄器
    """
    if format_string is None:
        format_string = '%(asctime)s [%(levelname)s] %(module)s: %(message)s'
    
    # 創建和配置 root logger
    logger = logging.getLogger()
    logger.setLevel(min(log_level, console_level))  # 設置為最低的級別
    
    # 清除現有處理程序
    logger.handlers.clear()
    
    # 創建格式器
    formatter = logging.Formatter(format_string)
    
    # 添加控制台處理程序
    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日誌文件，添加文件處理程序
    if log_file:
        # 確保日誌目錄存在
        log_file_path = Path(log_file)
        log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(str(log_file_path), encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # 記錄啟動信息
    logger.info('='*50)
    logger.info(f'Starting application at {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    logger.info(f'Python version: {sys.version}')
    logger.info(f'Operating system: {os.name} {sys.platform}')
    if log_file:
        logger.info(f'Log file: {log_file}')
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    獲取指定名稱的日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        
    Returns:
        日誌記錄器
    """
    return logging.getLogger(name)


# 防止模塊被直接運行
if __name__ == "__main__":
    print("此模塊不應直接運行")
    sys.exit(1) 