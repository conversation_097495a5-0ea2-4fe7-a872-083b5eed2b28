#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
出貨記錄工具 - 應用主類

此模塊包含應用程序的主類，負責初始化和管理應用程序的各個組件。
包括創建並管理界面、處理認證和訂閱驗證、啟動數據同步等。
"""

import os
import sys
import logging
import signal
from typing import Dict, Any
from pathlib import Path

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, Qt

from src.ui.login_window import LoginWindow
from src.ui.main_window import MainWindow
from src.services.auth_service import AuthService
from src.utils.hardware import check_device_id


class Application:
    """
    應用程序主類，負責初始化和運行應用。
    """
    
    def __init__(self, env_info: Dict[str, Any]):
        """
        初始化應用程序。
        
        Args:
            env_info: 包含環境信息的字典，包括數據目錄、調試模式等
        """
        self.env_info = env_info
        self.data_dir = Path(env_info['data_dir'])
        self.debug_mode = env_info['debug_mode']
        self.mac_address = env_info['mac_address']
        
        # 初始化Qt應用
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("出貨記錄工具")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("Example Company")
        self.app.setOrganizationDomain("example.com")
        
        # 設置高DPI支持
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 設置樣式表
        self._setup_stylesheet()
        
        # 設置翻譯
        self._setup_translation()
        
        # 初始化服務
        self.auth_service = AuthService()
        
        # 初始化窗口
        self.login_window = None
        self.main_window = None
        
        # 設置信號處理
        self._setup_signal_handlers()
        
    def _setup_stylesheet(self):
        """設置應用樣式表"""
        stylesheet_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "resources", "styles", "main.qss"
        )
        if os.path.exists(stylesheet_path):
            with open(stylesheet_path, "r", encoding="utf-8") as f:
                self.app.setStyleSheet(f.read())
        else:
            logging.warning(f"樣式表文件不存在: {stylesheet_path}")
    
    def _setup_translation(self):
        """設置應用翻譯"""
        # 暫時僅支持繁體中文
        translator = QTranslator()
        # 後續可添加多語言支持
        self.app.installTranslator(translator)
    
    def _setup_signal_handlers(self):
        """設置信號處理器，處理應用程序終止信號"""
        # 處理Ctrl+C等中斷信號
        signal.signal(signal.SIGINT, self._signal_handler)
        if sys.platform != "win32":
            signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, sig, frame):
        """處理終止信號"""
        logging.info(f"收到信號 {sig}，準備關閉應用程序...")
        if self.main_window:
            self.main_window.close()
        elif self.login_window:
            self.login_window.close()
        self.app.quit()
    
    def _show_login_window(self):
        """顯示登入窗口"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self._on_login_successful)
        self.login_window.show()
    
    def _on_login_successful(self, user_info):
        """
        登入成功的處理函數
        
        Args:
            user_info: 用戶信息，包含用戶ID、用戶名、權限等
        """
        logging.info(f"用戶 {user_info['username']} 登入成功")
        
        # 關閉登入窗口
        if self.login_window:
            self.login_window.close()
            self.login_window = None
        
        # 檢查設備ID，確保單一登入
        device_id = check_device_id(self.mac_address)
        if not device_id:
            logging.error("設備ID驗證失敗")
            # 顯示錯誤並退出
            return
        
        # 顯示主窗口
        self.main_window = MainWindow(user_info, self.env_info)
        self.main_window.logout_requested.connect(self._on_logout_requested)
        self.main_window.show()
    
    def _on_logout_requested(self):
        """處理登出請求"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        
        # 重新顯示登入窗口
        self._show_login_window()
    
    def run(self):
        """
        運行應用程序
        
        Returns:
            int: 應用程序退出碼
        """
        # 顯示登入窗口
        self._show_login_window()
        
        # 運行應用程序事件循環
        return self.app.exec()


if __name__ == "__main__":
    print("此模塊不應直接運行，請運行 src/main.py")
    sys.exit(1) 